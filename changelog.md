# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.htm).
### Unreleased

### Added
[SIM-3069] - Add New theme variables in all repo of simplify

### Changed
[SIM-3229] - Common auditlog url integration

### Removed

### Fixed

## Released
0.3.44 18-08-2025


### Added

### Changed

### Removed

### Fixed
[SIM-3552] - validation of SIM Qty & Type changed

## Released
0.3.43 14-08-2025


### Added

### Changed
[SIM-3346] - on hold related suggested changes

### Removed

### Fixed

## Released
0.3.42 08-08-2025

### Added

### Changed
[SIM-3308] Correct localstorage keyname.

### Removed

### Fixed
[SIM-3229] - Common auditlog url integration
[SIM] - reverted package.json config for build

## Released
0.3.40 30-07-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3229] - Common auditlog url integration
[SIM] - reverted package.json config for build

## Released
=======
0.3.39 30-07-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3519] - Something went wrong message is shown for non-real SIM

## Released
0.3.38 28-07-2025

### Added

### Changed
[SIM-3493] ui-demo-suggestion-for-improvment

### Removed

### Fixed

## Released
0.3.37 24-07-2025

### Added
[IM-3086] identify-efferts-for-map

### Changed

### Removed

### Fixed

## Released
0.3.36 07-07-2025

### Added

### Changed
[SIM-3262] - Change URI of auditlog.

### Removed

### Fixed
[SIM-3270] bug fix client admin permissions

## Released
0.3.35 30-06-2025

### Added

### Changed
[SIM-3121] - Update create order page demo suggestions
[SIM-3121] - Update update order status demo suggestions
### Removed

### Fixed

## Released
0.3.34 25-06-2025

### Added
[SIM-3127] - Add order tab page
[SIM-3121] - Add create order page page
### Changed

### Removed

### Fixed

## Released
0.3.33 24-06-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3238] - Added solution for sim usage  

## Released
0.3.32 23-06-2025

### Added

### Changed

### Removed

### Fixed
[sim-3234] - 0.4.0, 0.4.1 and 0.5.0 suggested changes

## Released
0.3.31 20-06-2025

### Added
[SIM-2975] - Integration of Send SMS To Device
[SIM-2976] - Integration of Send Cancel Location
[SIM-2977] - Intergration of Send POD
[SIM-3184] - Added two new SIM Type

### Changed

### Removed

### Fixed

## Released
0.3.30 12-06-2025

### Added
[SIM-3175] - Pagination range page

### Changed

### Removed

### Fixed

## Released
0.3.29 10-06-2025

### Added

### Changed

### Removed

### Fixed
[SIM-3059] - Bulk operation feature removed approutes permission 

## Released
0.3.28 09-06-2025

### Added
[SIM-3059] - Bulk operation feature

### Changed

### Removed

### Fixed

## Released
0.3.27 02-06-2025

### Added
[SIM-3154] - Keycloak changes

### Changed

### Removed

### Fixed

## Released
0.3.26 29-05-2025

### Added
[SIM-3066] - Implement socket for the real time updates and perfomance improvement.

### Changed

### Removed

### Fixed

## Released
0.3.25 20-05-2025

### Added
[SIM-3080]- [UI] Integrate UI changes for Respective APIs

### Changed
fix first two columns and action column of sim management tab
Add bulk operation tab
Add tab view for clint sime management page

### Removed

### Fixed

## Released
0.3.24 29-04-2025

### Added

### Changed
[SIM-2823] - node and npm update

### Removed

### Fixed
[SIM-2936] - trivy fixed

## Released
0.3.23 21-04-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2980] - payload change in bulk and single sim status api, bulk update imsi button position change 
[SIM-2991] - add validation message for Active or Pending SIM Status

## Released
0.3.22 08-04-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2950] - [UI] - Resolve the issue when Try to Update Bulk Sim Status
[SIM] - Validaiton for single MSISDN update.

## Released
0.3.21 02-04-2025

### Added
[SIM-2676] - Added Selection bulk MSISDN feature

### Changed

### Removed

### Fixed

## Released
0.3.20 28-03-2025

### Added

### Changed
[SIM] - Removed account api dependency

### Removed

### Fixed

## Released
0.3.19 27-03-2025

### Added

[SIM-2676] - As a BT Admin, I would like to assign bulk MSISDN
[SIM-2678] - As a BT Admin, I would like to assign single MSISDN to specific SIM 
[SIM-2904] - As a BT Admin, I would like to create IMSI ranges

### Changed

### Removed

### Fixed

## Released
0.3.18 31-01-2025

### Added

### Changed
[SIM] - Sorting in RatePlans List

### Removed

### Fixed

## Released
0.3.17 30-01-2025

### Added
[SIM-2783] - Show the default rate plan in the allocation process

### Changed

### Removed

### Fixed

## Released
0.3.16 22-01-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2062] - Top bar item showing on wrong place while switching modules

## Released
0.3.15 21-01-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2785] - Unable to fetch reallocate account rate plan when try to re-allocate to another account

### Released
0.3.14 01-01-2025

### Added

### Changed

### Removed

### Fixed

## Released
0.3.13 08-08-2024

### Added
[SPOG-2456] - Rate Plans by Accounts given 500 ERROR on PROD env

### Changed

### Removed

### Fixed

## Released
0.3.12 06-08-2024

### Added
[SPOG-2185] - Add Audit log coloumns

### Changed

### Removed

### Fixed

## Released
0.3.11 13-06-2024

### Added
[SPOG-2414] - Configure GitLab Merge Request Template

### Changed

### Removed

### Fixed

[Spog-1633] -"1 row selected" text is shown when click on SIM detail (now checkbox not available)

## Released
0.3.10 20-05-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2383] - SIM-Management UI IMSI range according to Permission.
[SPOG-2378] - If permission is not there on Tab then that tab won't appear in UI

## Released
0.3.9 08-04-2024

### Added

### Changed

### Removed

### Fixed
[SPOG] - Solved url issue for CLIENT user

## Released
0.3.8 08-04-2024

### Added

### Changed

### Removed

### Fixed

## Released
0.3.7 08-04-2024

### Added

### Changed

### Removed

### Fixed
- Solved cross browser conflict for file upload.
## Released
0.3.5 05-04-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2065] - [UI] As a BT user I want to see Error Type for each IMSI in the Invalid IMSI file, and be able to upload the same file after correction

## Released
0.3.4 28-03-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2264]- [UI] Need to set UI according to permissions.

## Released
0.3.3 14-03-2024

### Added
[SPOG-2215] As a CU or CA user I don't have access to my SIMs

### Changed

### Removed

### Fixed
- Forbidden page for clientAdmin solved.

## Released
0.3.2 29-02-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2120]-change permission key && Refactoring authorization based on demo

## Released
0.3.1 29-02-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2120]-change permission key && Refactoring authorization based on demo

### Released
0.3.0 05-01-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2037]- Fix date picker dropdown behaviour
[SPOG-2038]- allocation bug fix

## Released
0.2.38 05-01-2024

### Added
[SPOG-1631]- lable change for 3FF 4FF
[SPOG-1990]- Integrate APIs for SIMDetailHistory,ConnectionHistory, SMS and Voice for BT user/customer
[SPOG-2007]- Need to enable months of 2023 on date picker in all pages

### Changed

### Removed

### Fixed

## Released
0.2.37 01-12-2023

### Added
[SPOG-1937]- Access to TIMS - not working for customer
[SPOG-1863]-As a BT user, I want to allocate an IMSI range (segment) to a Customer Account using CSV file
[SPOG-1950]- In Voice connection History -Call Minutes should be displayed in the HH:MM:SS format
[SPOG-1983]-added authentication and authorization
### Changed

### Removed

### Fixed

## Released
0.2.36 06-11-2023

### Added
[SPOG-1834]- As a BT Customer, I want to see connection history with Voice and SMS CDRs
[SPOG-1836]- As a Customer user I want to have Market Share report available to me (0.2.4)
### Changed

### Removed

### Fixed

## Released
0.2.35 23-10-2023

### Added

### Changed

### Removed

### Fixed

## Released
0.2.34 20-10-2023

### Added

### Changed

### Removed
[SPOG-1835]-As a Customer user I don't want to see Market Share report in the very 1st release (0.2.3)
### Fixed

## Released
0.2.33 12-10-2023

### Added

### Changed

### Removed

### Fixed
[SPOG-1831]-fix EE% column aligment 
## Released
0.2.32 12-10-2023

### Added
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
[SPOG-1805]-Correction for Warning message in Market Share Reports
### Changed
[SPOG-1822] - Update EE% column data on Account page
[SPOG-1823] - Default zero value not showing on EE column in account page
### Removed

### Fixed

[SPOG-1801]- Fix call info Api only BT Client
[SPOG-1801]- intger value return with decimal scale
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
## Released
0.2.31 09-10-2023

### Added
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
[SPOG-1805]-Correction for Warning message in Market Share Reports
### Changed

### Removed

### Fixed
[SPOG-1801]- intger value return with decimal scale
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
## Released
0.2.30 09-10-2023

### Added
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
[SPOG-1805]-Correction for Warning message in Market Share Reports
### Changed

### Removed

### Fixed
[SPOG-1801]-As a BT/Account User, I want to see corrected formats for EE% column and for Data Usage (MB) in MSReports
## Released
0.2.29 06-10-2023

### Added

### Changed

### Removed

### Fixed
[1790]- Cosmetic changes in Marketshare Reports Modal
[SPOG-1793]- Data Rendering issue
## Released
0.2.28 28-09-2023

### Added

### Changed

### Removed

### Fixed
[SPOG-1481]-fix typo error 
## Released
0.2.27 27-09-2023

### Added

### Changed

### Removed

### Fixed

## Released
0.2.26 22-09-2023

### Added
[SPOG-1677]-BT Customer: Add EE % column on SIM Mgt
[SPOG-1729]-Only showing first 50 accounts in Account field on Create Allocation pop up.
[SPOG-1514]-BT Customer: Implement design to display Market Share Report per account
[SPOG-1515]-BT Customer: Implement design to display Market Share Report per SIM

[SPOG-1536]-[UI] only "Carrier" and "Country" columns and remove duplicates.
### Changed
[SPOG-1724]-change total_usage to totalUsage
### Removed

### Fixed
[SPOG-1677]-fix intager BT User: display "EE %" column inside customer sim
[SPOG-1283]-Add default placeholder for no data in the data grid
## Released
0.2.25 14-09-2023

### Added
[SPOG-1677]-BT Customer: Add EE % column on SIM Mgt
### Changed

### Removed

### Fixed

## Released
0.2.24 28-08-2023

### Added
[SPOG-1283] fix-placeholder
### Changed

### Removed

### Fixed
[SPOG-1618]- BT User, IMSI Allocations page is crashing
## Released
0.2.23 23-08-2023

### Added

### Changed
spog-1619-********-fix-imsi-allocation-header
### Removed

### Fixed

## Released
0.2.22 17-08-2023

### Added

### Changed
shared-component-from-2.21.1-to-2.27.0
### Removed

### Fixed

## Released
0.2.21 09-08-2023

### Added
[SPOG-1813]-ui-enable-code-coverage-to-all-micro-front-ends
### Changed

### Removed

### Fixed
disable future date in datepicker connection History Header
navagion on cellClick and button dialog display  on Click 
## Released
0.2.20  03-08-2023

### Added
accountNameSlice Add function
### Changed
change input props date as MMM-YY so it display like  JUL-23,
opento Props="month" DatePicker month view So control display as month then year
### Removed
remove checkbox selection from IMSIAllocation Table for BT User

### Fixed
display selection from August month
change column Imsi Allocation Table data align left to right quantity  Quantity Imsi first and Imsi last 
change column Imsi Ranges Table data align left to right quantity  Quantity Imsi first and Imsi last
Add commas value in Imsi Allocation 
change Allocation date to Allocation Date SimDetail Page
Active to Activate ToolTip for for toggle button 
add space for count in multiple  checkbox selection 
## Released
0.2.19 25-07-2023


### Added
Implement Thousand separator all values
### Changed

### Removed

### Fixed
change connection History and Audit trail Header First Activated and last activated date change DD-MM-YYYY
## Released
0.2.18 19-07-2023

### Added
Added Column Country Name,Carrier Name Connection History
### Changed
SPOG-1452 : Bind total row of sim management /IMSI Allocatino from simcards api instead of seprate api
### Removed

### Fixed

## Released
0.2.17 17-07-2023

### Added

### Changed
modify tooltips of export button

### Removed

### Fixed
SIM Management: Fix Export hanging for the filtered table
SIM Management: Fix Export with Search Records in file

## Released
0.2.16 14-07-2023

### Added

### Changed
fixed export button tooltips

### Removed

### Fixed

## Released
0.2.15 10-07-2023


### Added
create table ui of connection history of single sim
create table ui of audit trail of single sim
create ui of exportToSvg component for export file
create functionality of activation and deactivation sim lists table
create functionality of Total row in activate & deactivation sim lists table
replaces all mui html table into muiTable of nv2/Shared component table
format durration into mm:ss format
add comma seprated digits of number in form of money representiation
format date formate to specific format like "yyyy/mm/dd hh:mm:ss" and "yyyy/mm/dd"
create logic for convert "bytes" to "mb" 
format date with local time zone in connection history session start time and session end time
### Changed
Hide 'filter' icon from all tables

### Removed


## [Released]

### Added


### Changed

### Removed
