{"extends": ["stylelint-config-standard-scss"], "plugins": ["stylelint-scss"], "rules": {"at-rule-no-unknown": null, "block-closing-brace-newline-after": "always", "scss/at-rule-no-unknown": true, "at-rule-name-space-after": "always", "block-opening-brace-space-before": "always", "selector-list-comma-newline-after": null, "selector-class-pattern": null, "keyframes-name-pattern": null, "declaration-colon-newline-after": "always-multi-line"}}