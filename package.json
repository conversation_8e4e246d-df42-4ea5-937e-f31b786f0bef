{"name": "spog-sim-management", "version": "3.0.0594", "private": true, "dependencies": {"@babel/regjsgen": "^0.8.0", "@craco/craco": "^6.4.5", "@mui/icons-material": "^5.17.1", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^5.17.1", "@mui/styles": "^5.17.1", "@mui/x-date-pickers": "^5.0.20", "@nv2/nv2-pkg-js-shared-components": "^2.46.1", "@nv2/nv2-pkg-js-theme": "^2.11.3", "ajv": "^8.17.1", "axios": "^1.8.4", "core-js": "^2.6.12", "craco-sass-resources-loader": "^1.1.0", "dayjs": "^1.11.7", "formik": "^2.2.9", "jest-junit": "^16.0.0", "lodash": "^4.17.21", "postcss": "^8.4.16", "postcss-loader": "^7.3.4", "react": "^18.2.0", "react-cookie": "^4.1.1", "react-country-flag": "^3.0.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.2", "react-icons": "^4.12.0", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "react-toastify": "^9.1.1", "recharts": "^2.8.0", "sass-loader": "^13.3.3", "socket.io-client": "^4.8.1", "styled-components": "^5.3.11", "ts-jest": "^29.0.3", "typescript": "^5.8.3", "yup": "^0.32.11"}, "scripts": {"start": "GENERATE_SOURCEMAP=false PORT=3003 craco start", "start:on-windows": "set GENERATE_SOURCEMAP=false && set PORT=3003 && craco start", "build": "craco build", "build:standalone": "REACT_APP_STANDALONE=true craco build", "lint": "eslint --ext .ts --ext .tsx src", "lint:fix": "eslint --fix --ext .ts --ext .tsx src", "stylelint": "stylelint \"**/*.scss\"", "test": "jest --maxWorkers=10% --config ./jest.config.js --collectCoverage", "test:coverage": "CI=true npm test -- --env=jsdom --coverage", "eject": "react-scripts eject", "prepare": "husky install"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/plugin-proposal-export-default-from": "^7.18.10", "@babel/plugin-transform-runtime": "^7.18.10", "@babel/preset-env": "^7.18.10", "@babel/preset-react": "^7.18.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.2.4", "@types/node": "^22.14.0", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "@types/styled-components": "^5.1.26", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "axios-mock-adapter": "^2.1.0", "eslint": "^9.24.0", "eslint-config-airbnb": "^19.0.4", "husky": "^9.1.7", "identity-obj-proxy": "^3.0.0", "jest": "^29.1.2", "jest-css-modules-transform": "^4.4.2", "jest-environment-jsdom": "^29.0.3", "stylelint": "^14.16.1", "stylelint-config-standard-scss": "^5.0.0", "stylelint-scss": "^4.7.0"}, "overrides": {"form-data": "^4.0.4"}}