import { useAppContext } from 'AppContextProvider';
import React from 'react';

const SimIconHeader = () => {
  const { currentTheme } = useAppContext();
  return (
    <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.5746 16.8008C12.3851 16.8008 9.79956 19.3863 9.79956 22.5758V29.9258C9.79956 33.1153 12.3851 35.7008 15.5746 35.7008H25.0246C28.214 35.7008 30.7996 33.1153 30.7996 29.9258V22.5758C30.7996 19.3863 28.214 16.8008 25.0246 16.8008H15.5746ZM12.9496 22.5758C12.9496 21.1259 14.1248 19.9508 15.5746 19.9508H20.2996V25.2008H12.9496V22.5758ZM12.9496 28.3508H20.2996V32.5508H15.5746C14.1248 32.5508 12.9496 31.3756 12.9496 29.9258V28.3508ZM23.4496 32.5508V19.9508H25.0246C26.4744 19.9508 27.6496 21.1259 27.6496 22.5758V29.9258C27.6496 31.3756 26.4744 32.5508 25.0246 32.5508H23.4496Z" fill="url(#paint0_linear_2130_8068)" />
      <path d="M10.325 0C6.55565 0 3.5 3.05565 3.5 6.825V35.175C3.5 38.9443 6.55565 42 10.325 42H30.275C34.0443 42 37.1 38.9443 37.1 35.175V15.2996C37.1 13.4895 36.381 11.7536 35.101 10.4736L26.6265 1.99899C25.3465 0.719061 23.6104 0 21.8004 0H10.325ZM6.65 6.825C6.65 4.79535 8.29535 3.15 10.325 3.15H21.8004C22.7751 3.15 23.7098 3.53718 24.399 4.22638L32.8735 12.701C33.5628 13.3902 33.95 14.325 33.95 15.2996V35.175C33.95 37.2046 32.3046 38.85 30.275 38.85H10.325C8.29535 38.85 6.65 37.2046 6.65 35.175V6.825Z" fill="url(#paint1_linear_2130_8068)" />
      <defs>
        <linearGradient id="paint0_linear_2130_8068" x1="9.79956" y1="23.3243" x2="30.7996" y2="23.3243" gradientUnits="userSpaceOnUse">
          <stop stopColor={currentTheme?.primaryColor} />
          <stop offset="1" stopColor={currentTheme?.purpleColor} />
          <stop offset="1" stopColor={currentTheme?.purpleColor} />
        </linearGradient>
        <linearGradient id="paint1_linear_2130_8068" x1="3.5" y1="14.4968" x2="37.1" y2="14.4968" gradientUnits="userSpaceOnUse">
          <stop stopColor={currentTheme?.primaryColor} />
          <stop offset="1" stopColor={currentTheme?.purpleColor} />
          <stop offset="1" stopColor={currentTheme?.purpleColor} />
        </linearGradient>
      </defs>
    </svg>
  )
};

export default SimIconHeader;
