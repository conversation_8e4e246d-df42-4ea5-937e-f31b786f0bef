import { useAppContext } from 'AppContextProvider';
import React from 'react';

interface IPieChart {
  className?: string;
}

const PieChart = ({ className }: IPieChart) => {
  const { currentTheme } = useAppContext();
  return (
    <svg className={className} width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_118_6)">
        <path
          fillRule="evenodd"
          clipRule="evenodd" d="M13.0004 -0.000823975H14.0004C19.5232 -0.000823975 24.0004 4.47633 24.0004 9.99918V10.9992H13.0004V-0.000823975ZM15.0004 2.06107V8.99918H21.9385C21.4872 5.38046 18.6191 2.51232 15.0004 2.06107ZM9.00037 6.06107C5.05405 6.55317 2.00037 9.91956 2.00037 13.9992C2.00037 18.4175 5.58209 21.9992 10.0004 21.9992C11.8495 21.9992 13.551 21.3727 14.9061 20.3191L9.31775 14.7308L9.31434 14.7274L9.31194 14.725L9.31118 14.7242L9.31042 14.7234L9.30756 14.7206L9.30496 14.718L9.30152 14.7145L9.3005 14.7135L9.29955 14.7126L9.29867 14.7117L9.29784 14.7109L9.29709 14.7101L9.29608 14.7091L9.29523 14.7082L9.29432 14.7073L9.29358 14.7066L9.00037 14.4134V6.06107ZM11.0004 13.585L11.0004 3.99918H10.0004C4.47752 3.99918 0.000366211 8.47633 0.000366211 13.9992C0.000366211 19.522 4.47752 23.9992 10.0004 23.9992C12.7615 23.9992 15.2629 22.8788 17.0714 21.0702L17.7785 20.3631L11.0004 13.585ZM11.5548 11.9992H23.0004V12.9992C23.0004 15.4469 22.1196 17.6916 20.6589 19.4296L19.9637 20.2568L11.5548 11.9992ZM16.4459 13.9992L19.7703 17.2638C20.3814 16.296 20.7904 15.1884 20.9386 13.9992H16.4459Z"
          fill={currentTheme?.primaryColor || "#5514B4"} />
      </g>
      <defs>
        <clipPath id="clip0_118_6">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
};

PieChart.defaultProps = {
  className: '',
};

export default PieChart;
