.latest-dropzone {
  height: 150px;
  width: 400px;
  border: 2px dashed $light-color-300;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 12px;

    .MuiTypography-root {
      font-size: 13px;
      color: $dark-3;
    }
  }

  &__text {
    color: var(--primary-color, $brand-blue-color-500);
    margin-right: 16px !important;
    margin-left: 6px !important;

    &-sub {
      color: $dark-color-300;
      margin-bottom: 10px !important;
    }
  }

  svg {
    fill: $dark-52;
  }

  &:hover {
    // background-color: rgb(223 222 244 / 50%);
    border: 2px dashed var(--primary-color, $brand-blue-color-500);

    svg {
      fill: var(--primary-color, $brand-blue-color-400);
    }
  }

  &__preview {
    display: flex;
    align-items: center;
    position: relative;
    border-radius: 4px;
    margin-left: 60px;

    &-img {
      margin-right: 50px;
    }
  }

  &__description {
    color: $dark-69;
  }

  .MuiButtonBase-root {
    text-transform: capitalize;
  }
}

.latest-dropZone_error {
  padding-bottom: 10px;
}

@media screen and (max-width: 1600px) {
  .latest-dropzone__description {
    font-size: 13px !important;
  }
}
