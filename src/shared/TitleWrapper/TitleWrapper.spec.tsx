import React from 'react';
import { render, screen } from '@testing-library/react';
import TitleWrapper from './TitleWrapper';

describe('TitleWrapper Component', () => {
  it('renders the title correctly', () => {
    render(<TitleWrapper title="Test Title" />);

    const titleElement = screen.getByText('Test Title');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveStyle(`
      font-weight: 700;
      color: #333;
      padding: 12px 15px;
      border-radius: 4px;
      font-size: 13px;
      background-color: #F5F5F9;
    `);
  });

  it('applies the custom className', () => {
    render(<TitleWrapper title="Test Title" className="custom-class" />);

    const titleElement = screen.getByText('Test Title');
    expect(titleElement).toHaveClass('custom-class');
  });
});
