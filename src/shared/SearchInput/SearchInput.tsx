import React, { ChangeEvent, FC } from 'react';
import { AiOutlineSearch } from 'react-icons/ai';
import { InputBase, styled } from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';

import './SearchInput.scss';

const SearchWrapper = styled('div')(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${styles.inputBorderColor}`,
  '&:focus-within': {
    border: `1px solid ${theme.palette.primary.main}`,
  },
}));

const StyledInputBase = styled(InputBase)(({ theme }) => ({
  color: 'inherit',
  width: '100%',
  height: '40px',
  '& .MuiInputBase-input': {
    padding: theme.spacing(1, 1, 1, 0),
    fontSize: '14px',
    paddingLeft: `calc(1em + ${theme.spacing(4)})`,
    width: '100%',
  },
}));

interface ISearchInputProps {
  placeholder: string;
  value: string;
  onChange: (event: ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
}

const SearchInput: FC<ISearchInputProps> = ({ placeholder, value, onChange }) => (
  <SearchWrapper className="search">
    <div className="search__icon-wrapper">
      <AiOutlineSearch size={20} color={styles.darkColor300} />
    </div>
    <StyledInputBase
      placeholder={placeholder}
      inputProps={{ 'aria-label': 'search' }}
      value={value}
      onChange={onChange}
    />
  </SearchWrapper>
);

export default SearchInput;
