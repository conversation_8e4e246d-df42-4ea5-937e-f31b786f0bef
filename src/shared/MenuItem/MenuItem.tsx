import React from 'react';
import MenuItem from '@mui/material/MenuItem';
import { IconButton, Menu } from '@mui/material';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import { useAppContext } from 'AppContextProvider';

interface IMenuStatus {
  isActiveEnable: boolean, isDeactiveEnable: boolean,
}
interface ICustomizedMenus {
  actionOnBunchSims: (param: string) => void,
  menuStatus: IMenuStatus
}

const CustomizedMenus = ({ actionOnBunchSims, menuStatus }: ICustomizedMenus) => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { currentTheme } = useAppContext();

  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  return (
    <div>
      <IconButton
        sx={{ backgroundColor: currentTheme?.bgLightPrimary || "#f5f1fa" }}
        onClick={handleClick}
      >
        <MoreHorizIcon />
      </IconButton>
      <Menu
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        PaperProps={{
          elevation: 0,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.32))',
            mt: 1.5,

            '& .MuiPaper-root': {
              left: '1700px',
              width: '158px',
            },
            '& .MuiList-root': {
              display: 'flex',
              flexDirection: 'column',
              gap: '10px',
              marginRight: '30px',
              marginTop: '15px',
              marginLeft: '12px',
              marginBottom: '15px',
              fontFamily: 'BT Curve, sans-serif',
              fontStyle: 'normal',
              fontWeight: 400,
              fontSize: '14px',
              lineHeight: '140%',
              color: '#333333',

            },

          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem
          disabled={!menuStatus.isDeactiveEnable}
          disableRipple
          onClick={() => actionOnBunchSims('Activate')}
        >
          Activate
        </MenuItem>

        <MenuItem
          disabled={!menuStatus.isActiveEnable}
          disableRipple
          onClick={() => actionOnBunchSims('Deactivated')}
        >
          Deactivate
        </MenuItem>

      </Menu>
    </div>
  );
};
export default CustomizedMenus;
