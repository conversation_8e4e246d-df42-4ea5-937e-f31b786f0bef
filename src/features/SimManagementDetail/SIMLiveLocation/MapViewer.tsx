import React, { useEffect, useState } from 'react'
import { Box, Grid, Typography } from '@mui/material'
import {
  useParams,
} from 'react-router-dom';
import useAbortController from 'core/hooks/useAbortController';
import useMuiTableSearchParams from 'core/hooks/useMuiTableSearchParams';
import { paths } from 'core/configs/paths';
import { ILocationHistory } from './LocationHistory.models';
import { useLocation, useNavigate } from 'react-router-dom';
import { REPOSITORY, RESPONSE, ROUTE_PERMISSION } from 'core/utilities/constants';
import FileIcon from 'assets/images/FileIcon';
import { getLatestLocation, getSIMCurrentLocation, locationHistoryList } from './api.service';
import LocationHistory from './LocationHistory';
import ViewLatestDataSession from './ViewLatestDataSession';
import SimLocationMap from './SimLocationMap';
import { IMSI_DETAILS_TABS_INDEXES, IMSI_DETAILS_TABS_TABS } from '../SimManagement.models';
import { GetAuthorization } from 'PrivateRotes';

const SimLiveLocation = ({ selectedTab }) => {
  const [loadingSelectedLocation, setLoadingSelectedLocation] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<any>({ latitude: null, longitude: null });
  const { imsi } = useParams();
  const { cancelPreviousRequest, setNewController } = useAbortController();

  const [loadingHistoryList, setLoadingHistoryList] = useState(true);
  const [locationHistory, setLocationHistory] = useState<ILocationHistory[]>([]);
  const [rowCount, setRowCount] = useState(0);
  const [noData, setNoData] = useState({});

  const [loadingDataSession, setLoadingDataSession] = useState(true);
  const [latestSimLocation, setLatestSimLocation] = useState({});

  const gridMapView = GetAuthorization([ROUTE_PERMISSION.VIEW_CELL_LOCATION], [REPOSITORY.SIM_MANAGEMENT]);
  const gridLocationDetailsView = GetAuthorization([ROUTE_PERMISSION.VIEW_LATEST_LOCATION_DATA], [REPOSITORY.SIM_MANAGEMENT]);
  const gridListLocationsView = GetAuthorization([ROUTE_PERMISSION.VIEW_LOCATION_DATA], [REPOSITORY.SIM_MANAGEMENT]);

  const {
    generateParamsForUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useMuiTableSearchParams();
  const navigate = useNavigate();
  const location = useLocation();

  const getData = async (page, pageSize, field, sort, search) => {
    if (!imsi || Number.isNaN(Number(imsi))) {
      navigate(paths.notFound);
    }
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(page, pageSize, field, sort, search);

    newSearchParams.set('tab', IMSI_DETAILS_TABS_TABS.SIM_LOCATION);

    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;

    navigate(newUrl, { replace: true });
    try {
      setLoadingHistoryList(true);
      const newResponse: any = await locationHistoryList(imsi as string, signal, page,
        pageSize, field, sort, search);
      const { data } = newResponse;
      setRowCount(data?.totalCount);
      setLocationHistory(data?.results);
    } catch (errorObj: any) {
      setRowCount(0);
      setLocationHistory([]);
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.LOCATION_HISTORY_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      setNoData({
        icon: <FileIcon />,
        title: 'No location history found.',
        description: message,
      });

    } finally {
      setLoadingHistoryList(false);
    }
  };

  const loadLocationHistory = async () => {
    try {
      const {
        page, pageSize, field, sort, search,
      } = getParamsFromUrl();
      await getData(page, pageSize, field, sort, search);
    } catch (error) {
      console.error("Error during initial load:", error);
      setRowCount(0);
      setLocationHistory([]);
      setNoData({
        icon: <FileIcon />,
        title: 'Error loading data',
        description: 'An error occurred while fetching the location history.',
      });
    }
  }

  const loadLatestSimLocation = async () => {
    if (!imsi || Number.isNaN(Number(imsi))) {
      navigate(paths.notFound);
    }
    setLoadingDataSession(true);
    cancelPreviousRequest();
    const { signal } = setNewController();
    try {
      const newResponse: any = await getLatestLocation(imsi as string, signal);
      setLatestSimLocation(newResponse?.data);
      setLoadingDataSession(false);
    } catch (errorObj: any) {
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_AUIDITTRAIL_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      console.log("Error loading latest SIM location:", message);
    }
    finally {
      setLoadingDataSession(false);
    }
  };

  const loadSIMCurrentLocation = async () => {
    if (!imsi || Number.isNaN(Number(imsi))) {
      navigate(paths.notFound);
    }
    setLoadingSelectedLocation(true);
    cancelPreviousRequest();
    const { signal } = setNewController();
    try {
      setLoadingSelectedLocation(true);
      const { data }: any = await getSIMCurrentLocation(imsi as string, signal);
      setSelectedLocation({ latitude: data.lat, longitude: data.lon, data: data });
      setLoadingSelectedLocation(false);
    } catch (errorObj: any) {
      console.error("Error refreshing location:", errorObj);
      setSelectedLocation({ latitude: null, longitude: null, data: null }); // Reset to a default location in case of error
      let message = '';
      if (errorObj?.response?.status === 404) {
        message = RESPONSE.GET_AUIDITTRAIL_EMPTY;
      } else if (errorObj?.response?.status === 500) {
        message = RESPONSE.HTTP_STATUS_500;
      } else {
        message = errorObj?.response?.data?.detail;
      }
      console.log("Error loading latest SIM location:", message);
    }
    finally {
      setLoadingSelectedLocation(false);
    }

  }

  const handleRefreshLocation = async () => {
    try {
      await loadLocationHistory();
    } catch (e) {
      console.error("Error loading initial history:", e);
    }
    try {
      await loadLatestSimLocation();
    } catch (e) {
      console.error("Error loading data session:", e);
    }
    try {
      loadSIMCurrentLocation();
    } catch (e) {
      console.error("Error loading latest history:", e);
    }
    // Optionally, you can handle errorOccurred if you want to show a message
  };

  useEffect(() => {
    handleRefreshLocation();
  }, [imsi, selectedTab]);

  useEffect(() => {
    if (selectedTab !== undefined && selectedTab !== IMSI_DETAILS_TABS_INDEXES.SIM_LOCATION && (!imsi || Number.isNaN(Number(imsi)))) {
      navigate(paths.notFound);
    };
  }, []);


  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    getData(page, pageSize, field, sort, search);
  };

  return (
    <Box sx={{ p: "12px" }}>
      <Box sx={{ my: "16px" }}>
        <Typography variant="h3">Current Location</Typography>
      </Box>
      <Grid container gap={4}>
        {gridMapView && (
          <Grid
            item
            xs={12}
            sm={12}
            md={gridMapView && gridLocationDetailsView ? 6 : 12}
            sx={{ display: "flex", flexDirection: "column", height: "100%" }}
          >
            <Box sx={{ flex: 1, height: "100%" }}>
              <SimLocationMap
                loading={loadingSelectedLocation}
                location={selectedLocation}
              />
            </Box>
          </Grid>
        )}
        {gridLocationDetailsView && (
          <Grid
            item
            xs={12}
            sm={12}
            md={gridMapView && gridLocationDetailsView ? 5.8 : 12}
            sx={{
              border: "1px solid #DEDEE6",
              borderRadius: "4px",
              p: "30px",
              display: "flex",
              flexDirection: "column",
              height: "100%",
            }}
          >
            <Box sx={{ flex: 1, height: "100%" }}>
              <ViewLatestDataSession
                handleRefreshLocation={handleRefreshLocation}
                dataSession={latestSimLocation}
                loading={loadingDataSession}
              />
            </Box>
          </Grid>
        )}
        {gridListLocationsView && (
          <Grid item xs={12} sm={12} md={12}>
            <Box sx={{ my: "16px" }}>
              <Typography variant="h3">SIM Location History</Typography>
            </Box>
            <LocationHistory
              defaultPagination={defaultPagination}
              defaultSort={defaultSort}
              initialSearchValue={initialSearchValue ?? ""}
              locationHistory={locationHistory}
              loading={loadingHistoryList}
              rowCount={rowCount}
              onChange={onChange}
              noData={noData}
            />
          </Grid>
        )}
      </Grid>
    </Box>
  );
}

export default SimLiveLocation;