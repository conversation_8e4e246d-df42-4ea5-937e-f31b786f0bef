import React from 'react';
import Skeleton from '@mui/material/Skeleton';
import Box from '@mui/material/Box';
import SimIcon from 'assets/images/SimIcon';
import ReactLeafletMapViewer from '@nv2/nv2-pkg-js-shared-components/lib/ReactLeafletMapViewer';
import NoDataFound from 'shared/NoDataFoundWithIcon/NoDataFound';

const SimLocationMap = ({ loading, location }) => {
  if (loading) {
    return <Skeleton variant="rectangular" width="100%" height={400} />;
  }
  const optionalData = {
    'Lat': location?.latitude.toFixed(6) || '-',
    'Lon': location?.longitude.toFixed(6) || '-',
    'Cell': location?.data?.cell || '-',
    'Lac': location?.data?.lac || '-',
    'Cell Range': location?.data?.range || '-',
  }
  if (location.latitude && location.longitude) {
    return (
      <ReactLeafletMapViewer
        latitude={location.latitude}
        longitude={location.longitude}
        zoom={15}
        height="420px"
        width="100%"
        showMarker
        data={optionalData}
      />
    );
  }
  return (
    <Box sx={{ width: '100%', height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', border: '1px solid #DEDEE6', borderRadius: '4px', bgcolor: '#fafafd' }}>
      <NoDataFound
        icon={<SimIcon width={80} height={80} />}
        text="No location detail found"
        description=""
      />
    </Box>
  );
};

export default SimLocationMap;