import {
  Box,
  Button,
  IconButton,
  Skeleton,
  Stack,
  <PERSON><PERSON>ield,
  Typo<PERSON>,
} from "@mui/material";
import useAbortController from "core/hooks/useAbortController";
import { getRelativeTime } from "core/utilities/formatDate";
import { toastError } from "core/utilities/toastHelper";
import dayjs from "dayjs";
import {
  getSimActionByImsi,
  sendSMS,
} from "features/SimManagement/api.service";
import IMSIAllocationsContextProvider from "features/SimManagement/IMSIAllocations/IMSIAllocationsContext";
import { useFormik } from "formik";
import useMuiTableSearchParams from "hooks/useMuiTableSearchParams";
import React, {
  useCallback,
  useContext,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from "react";
import { GrChat, GrSend } from "react-icons/gr";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import Loader from "shared/Loader";
import * as Yup from "yup";
import { SimDetailContex } from "./Context/SimDetailContex";
import "./sendSMSToDevice.scss";
import { IMSI_DETAILS_TABS_TABS } from "./SimManagement.models";
import { useAppContext } from "AppContextProvider";

const smsValidationSchema = Yup.object({
  message: Yup.string()
    .required("Message is required")
    .max(240, "Message cannot exceed 240 characters")
    .trim(),
});

const dummySkeletonMessage = [{ uuid: "01" }, { uuid: "02" }, { uuid: "03" }];

const SendSMSToDevice = () => {
  const [messages, setMessages] = useState<any>(dummySkeletonMessage);
  const [isLoading, setIsLoading] = useState(false);
  const [skeletonLoader, setSkeletonLoader] = useState(false);
  const [initialLoadDone, setInitialLoadDone] = useState(false);
  const [page, setPage] = useState(1);
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const [hasMore, setHasMore] = useState(true);
  const { imsi } = useParams();
  const { currentTheme } = useAppContext();
  const { generateParamsForUrl, getParamsFromUrl } = useMuiTableSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const user = localStorage.getItem("userDetails");
  const userProfile = user ? JSON.parse(user) : "";
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const pageSize = 10;
  const { date } = useContext(SimDetailContex);

  const formik = useFormik({
    initialValues: {
      message: "",
    },
    validationSchema: smsValidationSchema,
    onSubmit: (values, { resetForm }) => {
      handleSend(values.message);
      resetForm();
    },
  });

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const getData = async (
    currentPage,
    currentPageSize,
    field,
    search,
    shouldAppend = false
  ) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(
      currentPage,
      currentPageSize,
      field,
      search,
      "",
      "sms_"
    );
    newSearchParams.set("tab", IMSI_DETAILS_TABS_TABS.DIAGNOSTICS);
    const newUrl = `${location.pathname}?${newSearchParams.toString()}`;
    navigate(newUrl, { replace: true });
    const payload = {
      simAction: ["SMS"],
      imsi: [imsi],
    };
    try {
      setIsLoading(true);
      const { data } = await getSimActionByImsi(
        payload,
        null, // passed null for month to get all the records
        currentPage,
        currentPageSize,
        field,
        search,
        signal
      );

      if (
        [...data?.results, ...messages].length === data?.totalCount ||
        pageSize >= data?.totalCount
      ) {
        setHasMore(false);
      } else {
        setHasMore(true);
      }

      if (shouldAppend) {
        setMessages((prevMessages) => [
          ...data?.results?.reverse(),
          ...prevMessages,
        ]);
      } else {
        setMessages(data?.results?.reverse());
      }
    } catch (errorObj: any) {
      toastError(errorObj?.response?.data?.detail);
    } finally {
      setIsLoading(false);
      setInitialLoadDone(true);
    }
  };

  const refreshData = (shouldReset = true) => {
    if (shouldReset) {
      setPage(1);
      getData(1, pageSize, "-created_at", null, false);
    } else {
      getData(page, pageSize, "-created_at", null, false);
    }
  };

  const loadMoreMessages = () => {
    if (isLoading || !hasMore) return;

    const container = messagesContainerRef.current;
    const previousScrollHeight = container?.scrollHeight || 0;

    const nextPage = page + 1;
    setPage(nextPage);

    getData(nextPage, pageSize, "-created_at", null, true).then(() => {
      setTimeout(() => {
        const newScrollHeight = container?.scrollHeight || 0;
        const heightDiff = newScrollHeight - previousScrollHeight;
        if (container) {
          container.scrollTop = heightDiff;
        }
      }, 0);
    });
  };

  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const { scrollTop } = messagesContainerRef.current;
    if (scrollTop < 10 && hasMore && !isLoading && initialLoadDone) {
      loadMoreMessages();
    }
  }, [hasMore, isLoading, page]);

  useEffect(() => {
    if (!initialLoadDone) {
      refreshData();
    }
  }, []);

  useLayoutEffect(() => {
    if (!isLoading && page === 1) {
      scrollToBottom();
    }
  }, [messages]);

  useEffect(() => {
    const messagesContainer = messagesContainerRef.current;
    if (messagesContainer && initialLoadDone && messages.length > 0) {
      messagesContainer.addEventListener("scroll", handleScroll);
      return () =>
        messagesContainer.removeEventListener("scroll", handleScroll);
    }
  }, [handleScroll]);

  const handleSend = async (message: string) => {
    setIsLoading(true);
    setSkeletonLoader(true);
    if (!message.trim()) return;

    const newMessage = {
      imsi,
      message,
      createdBy: userProfile.email,
    };
    try {
      await sendSMS(newMessage);
      refreshData(true);
    } catch (error: any) {
      refreshData(true);
      toastError(error?.response?.data?.detail);
    } finally {
      setIsLoading(false);
      setSkeletonLoader(false);
    }
  };

  const NoMessagePreview = () => (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        width: "100%",
        height: "calc(-540px + 100vh)",
      }}
    >
      <Box
        sx={{
          borderRadius: "10px",
          p: "16px",
          backgroundColor: currentTheme?.bgLightPrimary || "#F5F1FA",
        }}
      >
        <GrChat size={40} />
      </Box>
      <Box textAlign="center">
        <Typography variant="h3">No Messages Found</Typography>
        <Typography variant="body2">
          You can send a new message using the text area below.
        </Typography>
      </Box>
    </Box>
  );

  return (
    <Box data-testid="audit-trail" className="send-sms-tab">
      <IMSIAllocationsContextProvider value={{ getData, getParamsFromUrl }}>
        <Box
          className=""
          sx={{
            mx: "auto",
            mt: 4,
            p: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              margin: "10px 0 25px 0",
            }}
          >
            <Typography variant="h3">SMS History</Typography>
          </Box>
          <Box sx={{ border: "1px solid #D0D1DC" }}>
            <Box
              sx={{
                textAlign: "center",
                backgroundColor: "#F5F5F9",
                height: "60px",
                borderBottom: "1px solid #D0D1DC",
                p: 2,
                display: "flex",
                flexDirection: "column",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography variant="body1" fontWeight={700}>
                {imsi}
              </Typography>
              {messages.length > 0 && (
                <Typography variant="caption">
                  {messages.length > 0
                    ? getRelativeTime(messages[messages.length - 1]?.createdAt)
                    : ""}
                </Typography>
              )}
            </Box>
            <Box
              ref={messagesContainerRef}
              className="scrollBar"
              sx={{
                px: 4,
                py: 2,
                height: "calc(100vh - 514px)",
                overflowY: "auto",
                overflowX: "hidden",
                position: "relative",
              }}
            >
              <Stack spacing={2}>
                {messages?.length > 0 && hasMore && page > 1 && isLoading && (
                  <Box sx={{ textAlign: "center", py: 1 }}>
                    <IconButton onClick={loadMoreMessages}>
                      <Loader size={24} staticColor="#D0D1DC" />
                    </IconButton>
                  </Box>
                )}
                {messages?.length > 0 ? (
                  messages.map((msg) => (
                    <>
                      {msg?.message && !skeletonLoader ? (
                        <Box
                          key={`sent-${msg.uuid}`}
                          sx={{
                            alignSelf: "flex-end",
                            maxWidth: "80%",
                          }}
                        >
                          <Box
                            className="message-bubble simplify-message"
                            sx={{ position: "relative" }}
                          >
                            <Box
                              sx={{
                                bgcolor: currentTheme?.btTextColor || "#A56DD7",
                                color: "#ffffff",
                                textAlign: "end",
                                px: 2,
                                py: 1.5,
                                borderRadius: "6px 0 6px 6px",
                                display: "flex",
                                flexDirection: "column",
                                gap: "10px",
                              }}
                            >
                              <Typography variant="body1" fontWeight={700}>
                                {msg?.createdBy}
                              </Typography>
                              <Typography variant="body2">
                                {msg.message}
                              </Typography>
                              <div
                                className="triangle"
                                style={{
                                  borderTopColor:
                                    currentTheme?.btTextColor || "#A56DD7",
                                }}
                              ></div>
                            </Box>
                          </Box>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            textAlign="end"
                            sx={{ mt: 0.5 }}
                          >
                            {dayjs(msg.createdAt).format(
                              "MMM D, YYYY [at] h:mm A"
                            )}
                          </Typography>
                        </Box>
                      ) : (
                        <Box
                          className="message-bubble simplify-message"
                          sx={{ display: "flex", justifyContent: "end" }}
                        >
                          <Skeleton height={100} width={300} />
                        </Box>
                      )}
                      {msg.response && !skeletonLoader ? (
                        <Box
                          key={`response-${msg.uuid}`}
                          sx={{
                            alignSelf: "flex-start",
                            maxWidth: "80%",
                          }}
                        >
                          <Box
                            className="message-bubble device-message"
                            sx={{ position: "relative" }}
                          >
                            <Box
                              sx={{
                                bgcolor: currentTheme?.actionBg || "#EBE3F6",
                                color: "#000000",
                                textAlign: "start",
                                px: 2,
                                py: 1.5,
                                borderRadius: "6px 6px 6px 0",
                                display: "flex",
                                flexDirection: "column",
                                gap: "10px",
                              }}
                            >
                              <Typography variant="body1" fontWeight={700}>
                                Device
                              </Typography>
                              <Typography variant="body2">
                                {msg.response}
                              </Typography>
                              <div
                                className="triangle"
                                style={{
                                  borderTopColor:
                                    currentTheme?.actionBg || "#ebe3f6",
                                }}
                              ></div>
                            </Box>
                          </Box>
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{ mt: 0.5 }}
                          >
                            {dayjs(msg.createdAt).format(
                              "MMM D, YYYY [at] h:mm A"
                            )}
                          </Typography>
                        </Box>
                      ) : (
                        <Box
                          className="message-bubble device-message"
                          sx={{ display: "flex", justifyContent: "start" }}
                        >
                          <Skeleton height={100} width={300} />
                        </Box>
                      )}
                    </>
                  ))
                ) : (
                  <NoMessagePreview />
                )}
                <div ref={messagesEndRef} />
              </Stack>
            </Box>
            <Box
              sx={{
                display: "flex",
                gap: 1,
                backgroundColor: "#F5F5F9",
                padding: 4,
                borderTop: "1px solid #D0D1DC",
              }}
            >
              <form
                onSubmit={formik.handleSubmit}
                style={{ width: "100%", display: "flex" }}
              >
                <TextField
                  fullWidth
                  sx={{
                    "& .MuiInputBase-input": {
                      p: "30px",
                      marginRight: "110px",
                    },
                    "& .Mui-error": {
                      color: "error.main",
                    },
                  }}
                  placeholder="Start typing message..."
                  id="message"
                  name="message"
                  value={formik.values.message}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                />
                <Button
                  type="submit"
                  size="small"
                  sx={{
                    position: "absolute",
                    right: "76px !important",
                    mt: "10px",
                  }}
                  variant="contained"
                  disabled={
                    formik.isSubmitting || !formik.isValid || !formik.dirty
                  }
                  startIcon={<GrSend size={24} />}
                >
                  Send SMS
                </Button>
              </form>
            </Box>
          </Box>
        </Box>
      </IMSIAllocationsContextProvider>
    </Box>
  );
};

export default SendSMSToDevice;
