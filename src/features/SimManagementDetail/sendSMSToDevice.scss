.scrollBar::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.scrollBar::-webkit-scrollbar-thumb {
  background-color: #dedee6;
  padding-inline: 2px;
  border-radius: 20px;
}

body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

body::-webkit-scrollbar-thumb {
  background-color: #c4c4ce;
  padding-inline: 2px;
  border-radius: 20px;
}

.send-sms-tab {
  .message-bubble {
    position: relative;
    z-index: 1;

    &.simplify-message {
      .triangle {
        position: absolute;
        width: 0;
        height: 0;
        border-bottom: 8px solid transparent;
        right: -6px;
        top: -17px;
        border-top: 20px solid;
        border-left: 20px solid transparent;
        transform: rotate(135deg);
        border-radius: 0 4px 0 0;
        z-index: -1;
      }
    }

    &.device-message {
      .triangle {
        position: absolute;
        width: 0;
        height: 0;
        border-top: 20px solid;
        border-left: 20px solid transparent;
        transform: rotate(135deg);
        border-radius: 0 4px 0 0;
        left: -10px;
        top: -10px;
        z-index: -1;
      }
    }
  }
}
