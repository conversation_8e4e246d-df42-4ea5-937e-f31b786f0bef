import { SxProps, Theme } from "@mui/material";
import { IUser } from "user.model";

export interface IHeader {
  id: string;
  name: string;
  width: number;
  align?: 'left' | 'right' | 'center';
  sticky?: boolean;
  valuePath?: string;
  sort?: boolean;
  field?: string;
}

export interface IStyledTableCellProps {
  bold?: boolean;
  align?: 'left' | 'right' | 'center';
  sticky?: boolean;
  th?: boolean;
}

export interface IIMSIRange {
  id: number;
  title: string;
  provider: string;
  formFactor: string;
  quantity: number;
  imsiFirst: string;
  imsiLast: string;
  remaining: number;
  createdAt: string;
  createdBy: string;
}

export interface IIMSIRangeResponse {
  page: number;
  pageSize: number;
  lastPage: number;
  totalCount: number;
  results: IIMSIRange[];
}

export interface IIMSIAllocation {
  id: number;
  title: string;
  quantity: number;
  provider: string;
  formFactor: string;
  createdAt: string;
  accountId: number;
  rangeId: number;
  accountName: string;
  country: string;  
  logoUrl: string;
}

export interface IIMSIAllocationResponse {
  page: number;
  pageSize: number;
  lastPage: number;
  totalCount: number;
  summary: string;
  results: IIMSIAllocation[];
}

export interface IIMSIAllocationBody {
  title: string;
  accountId: number;
  rangeId: number;
  ratePlanId: number;
  quantity: number;
}

export interface IRatePlan {
  id: number;
  name: string;
  accessFee: number;
  currency: string;
  isDefault: boolean;
}

export interface IRatePlanCompany {
  accountId: number;
  accountName: string;
  accountLogoUrl: string;
  ratePlans: IRatePlan[];
}

export interface IRateResponse<T> {
  result: T[];
}

export interface IOption {
  ref: string;
  name: string;
}

export interface IAccount {
  id: number;
  name: string;
  logoUrl: string;
  status: IOption;
  agreementNumber: string;
  currency: string;
  country: string;
  industryVertical: IOption;
  salesChannel: IOption;
  productTypes: IOption[];
  salesPerson: string;
  defaultRatePlan?: IRatePlan;
  contactName: string;
  email: string;
  phone: string;
  address1: string;
  address2: string;
  stateRegion: string;
  city: string;
  postcode: string;
}
export interface IAccountList {
  lastPage: number;
  totalCount?: number;
  results: IAccount[];
}
export enum CloseModalReason {
  BackdropClick = 'backdropClick',
  Confirm = 'confirm',
}

export interface ITab {
  [key: string]: number;
}

export interface ICardsRemains {
  provider: string;
  standard: number;
  micro: number;
  nano: number;
  esim_mff2: number;
  esim_mff2_euicc: number;
}

export interface ICard {
  simId: string;
  id: string;
  allocationReference: string;
  ratePlanId?: number;
  allocationDate: Date;
  simStatus: string;
  eeUsage: number;
  usage: number;
  iccid: string;
  msisdn: string;
  imsi: string;
  type: string;
  ratePlan: string | null;
  simProfile: IOption;
}
export interface IOperation {
  id: string;
  requestId: string;
  accountId: number;
  accountName: string;
  user: string;
  createdDate: string;
  clientIp: string;
  action: string;
  field: string;
  status: string;
}
export interface ICardsList {
  lastPage: number;
  page: number;
  pageSize: number;
  results: ICard[];
  totalCount: number;
  formFactor: string;
}

export enum DETAILS_FORM_SECTION_RATES_NAMES {
  DATA = 'data',
  VOICE_MO = 'voiceMo',
  VOICE_MT = 'voiceMt',
  SMS = 'sms',
}

export interface ISIMRequestBulk {
  imsi: string[];
  createdBy: string;
}
export interface ISimRequest {
  imsi: string;
  createdBy: string;
}
export interface ISimResponse {
  uuid: string;
  message: string;
  status: string;
}
 
export enum DETAILS_FORM_SECTION_EXTRA_NAMES {
   
  ORIGINATION_ZONES = 'originationZones',
}

export interface IRate {
  value: number | undefined;
  rangeFrom: number;
  rangeTo?: number | null | string;
  isAutoFocusEnabled?: boolean;
}

export interface IOriginationGroupData {
  rates: IRate[];
  rateModel?: string;
}

export interface IOriginationGroup {
  [DETAILS_FORM_SECTION_EXTRA_NAMES.ORIGINATION_ZONES]: string[];
  [DETAILS_FORM_SECTION_RATES_NAMES.DATA]: IOriginationGroupData;
  [DETAILS_FORM_SECTION_RATES_NAMES.VOICE_MO]: IOriginationGroupData;
  [DETAILS_FORM_SECTION_RATES_NAMES.VOICE_MT]: IOriginationGroupData;
  [DETAILS_FORM_SECTION_RATES_NAMES.SMS]: IOriginationGroupData;
}

export interface IRatePlanFull extends IRatePlan {
  accountId: number;
  originationGroups: IOriginationGroup[];
}

export interface IRatePlanFullClient extends IRatePlanFull {
  accountName: string;
}

export interface IErrors {
  sim: string;
  issue: string;
}

export interface IMSISDNErrors {
  msisdn: string;
  issue: string;
}

export interface IResponse {
  totalSIM: number;
  errorSIM: number;
  results: IErrors[];
}

export interface ISimDetail {
  quantity: number;
  simType: string;
}

export interface IOrderDetails {
  orderTracking: any;
  orderId: number;
  orderBy: string;
  customerAccountName: string;
  orderDate: string; // Consider using Date if you plan to parse it
  customerEmail: string;
  customerPhone: string;
  status: string | any;
  orderItem: ISimDetail[];
  customerAccountLogoUrl: string;
  comments: string | null;
}

export interface ISimDetailsFormValues {
  order_status: string;
  tracking_url: string | null;
  tracking_reference: string | null;
  reject_reason: string | null;
}

export interface SimOrderingInterface {
  user: IUser | undefined,
}

export interface IDetailsItemProps {
  label: string;
  value: React.ReactNode;
  customSx?: SxProps<Theme>;
  loader?: boolean;
}
export interface orderDetailsProps {
  user: IUser | undefined,
  open: boolean;
  onClose: Function;
  listFunction: Function;
  orderDetails: IOrderDetails | null;
}

export interface orderStatusChange {
  isChange: boolean;
  val: string;
}

export interface TextFieldWrapperProps {
  name: string;
  formikValue: any;
  label?: string;
  className?: string;
  [key: string]: any;
}
export interface ISimDetail {
  quantity: number;
  simType: string;
}

export interface IOrderDetails {
  orderTracking: any;
  orderId: number;
  orderUuid: string;
  orderBy: string;
  customerAccountName: string;
  orderDate: string; // Consider using Date if you plan to parse it
  customerEmail: string;
  customerPhone: string;
  status: string | any;
  orderItem: ISimDetail[];
  customerAccountLogoUrl: string;
  comments: string | null;
}

export interface ISimDetailsFormValues {
  order_status: string;
  tracking_url: string | null;
  tracking_reference: string | null;
  reject_reason: string | null;
}

export interface SimOrderingInterface {
  user: IUser | undefined,
}

export interface IDetailsItemProps {
  label: string;
  value: React.ReactNode;
  customSx?: SxProps<Theme>;
  loader?: boolean;
}
export interface orderDetailsProps {
  user: IUser | undefined,
  open: boolean;
  onClose: Function;
  listFunction: Function;
  orderDetails: IOrderDetails | null;
}

export interface orderStatusChange {
  isChange: boolean;
  val: string;
}

export interface ISimAction{
  id: string;
  imsi: string;
  iccid: string;
  msisdn: string;
  requestType: string;
  field: string;
  action: string;
  clientIp: string;
  createdBy: string;
  createdAt: string;
}
