import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { GrDirections } from 'react-icons/gr';
import Tabs from '@nv2/nv2-pkg-js-shared-components/lib/Tabs';
import TabPanel from '@nv2/nv2-pkg-js-shared-components/lib/TabPanel';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { Box, useTheme } from '@mui/material';
import FadeIn from 'shared/FadeIn';
import SimIcon from 'assets/images/SimIcon';
import { PERMISSION, REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { GetAuthorization } from 'PrivateRotes';
// import usePermission from 'hooks/usePermission';
import { AiOutlineMobile, AiOutlineShoppingCart, AiOutlineCloudServer } from 'react-icons/ai';
import IMSIAllocations from './IMSIAllocations';
import IMSIRanges from './IMSIRanges';
import { ITab } from './SimManagement.models';

import './SimManagement.scss';
import MSISDNMain from './MSISDN/MSISDNMain';
import SimOrdering from './SimOrdering';
import BulkOperationsTracker from './BulkOperationsTracker';
import { IUser } from 'user.model';
import { useAppContext } from 'AppContextProvider';

export const tabs: ITab = {
  'imsi-ranges': 0,
  'imsi-allocations': 1,
  'msisdn': 2,
  'bulk-operation': 3,
  'sim-order': 4,
};

export const TabsName = {
  IMSI_RANGES: 'imsi-ranges',
  IMSI_ALLOCATIONS: 'imsi-allocations',
  MSISDN: 'msisdn',
  BULK_OPERATION: 'bulk-operation',
  SIM_ORDER: 'sim-order',
};

export const tabItemsConfig = [
  {
    name: 'IMSI Ranges',
    icon: <SimIcon />,
  },
  {
    name: 'IMSI Allocations',
    icon: <GrDirections size={21} />,
  },
  {
    name: 'MSISDN',
    icon: <AiOutlineMobile />,
  },
  {
    name: 'Bulk Operations Tracker',
    icon: <AiOutlineCloudServer />,
  },
  {
    name: 'Sim Orders',
    icon: <AiOutlineMobile />,
  },
];

interface SimManagementProps {
  user: IUser | undefined,
}

const SimManagement = ({ user }: SimManagementProps) => {
  const theme = useTheme();
  const [tabIndex, setTabIndex] = useState(0);
  const [searchParams, setSearchParams] = useSearchParams();
  const { currentTheme } = useAppContext();
  const tabItemsConfigs = [
    {
      name: 'IMSI Ranges',
      icon: <SimIcon />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION.GET_RANGES || ROUTE_PERMISSION.GET_SIM_REMAINS],
        // [],
        [REPOSITORY.SIM_ORDERS]),
    },
    {
      name: 'IMSI Allocations',
      icon: <GrDirections size={21} />,
      disabled: !GetAuthorization(
        [
          ROUTE_PERMISSION.GET_ALLOCATIONS,
          ROUTE_PERMISSION.GET_RATE_PLANS,
          ROUTE_PERMISSION.VIEW_ACCOUNTS,
          ROUTE_PERMISSION.GET_ACCOUNT_NAMES,
        ],
        [
          REPOSITORY.SIM_ORDERS,
          REPOSITORY.SIM_MANAGEMENT,
          REPOSITORY.ACCOUNT_MANAGEMENT,
          REPOSITORY.RATE_PLAN,
        ],
      ),
    },
    {
      name: 'MSISDN',
      icon: <AiOutlineMobile size={21} />,
      disabled: !GetAuthorization(
        [PERMISSION?.MSISDN_POOL_DETAILS],
        [REPOSITORY?.SIM_MANAGEMENT],
      ),
    },
    {
      name: 'Bulk Operations Tracker',
      icon: <AiOutlineCloudServer size={21} />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION?.VIEW_TRACE_DATA],
        [REPOSITORY?.AUDIT],
      ),
    },
    {
      name: 'SIM Orders',
      icon: <AiOutlineShoppingCart size={21} />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION?.VIEW_ORDERS],
        [REPOSITORY?.SIM_ORDERS],
      ),
    },
  ];

  const selectedNewTab = async (event, newTabValue) => {
    setTabIndex(newTabValue);
    setSearchParams({ tab: Object.keys(tabs)[newTabValue] }, {
      replace: true,
    });
  };

  useEffect(() => {
    const tabFromQuery = searchParams.get('tab');
    const searchFromQuery = searchParams.get('search');
    if (tabFromQuery && tabs[tabFromQuery] !== tabIndex) {
      setTabIndex(tabs[tabFromQuery]);
      setSearchParams({ tab: tabFromQuery, search: searchFromQuery || '' }, {
        replace: true,
      });
    } else if (!tabFromQuery) {
      const defaultTabIndex = tabItemsConfigs?.findIndex((tab) => !tab.disabled);
      setSearchParams({ tab: Object.keys(tabs)[defaultTabIndex] }, {
        replace: true,
      });
    }
  }, [searchParams]);

  const TabComponent = [
    {
      components: <IMSIRanges />,
      permission: [ROUTE_PERMISSION.GET_RANGES || ROUTE_PERMISSION.GET_SIM_REMAINS],
      repository: [REPOSITORY.SIM_ORDERS],
    },
    {
      components: <IMSIAllocations />,
      permission: [
        ROUTE_PERMISSION.GET_ALLOCATIONS,
        ROUTE_PERMISSION.GET_RATE_PLANS,
        ROUTE_PERMISSION.VIEW_ACCOUNTS,
        ROUTE_PERMISSION.GET_ACCOUNT_NAMES,
      ],
      repository: [
        REPOSITORY.SIM_ORDERS,
        REPOSITORY.SIM_MANAGEMENT,
        REPOSITORY.ACCOUNT_MANAGEMENT,
        REPOSITORY.RATE_PLAN,
      ],
    },
    {
      components: <MSISDNMain />,
      permission: [PERMISSION?.MSISDN_POOL_DETAILS],
      repository: [REPOSITORY?.SIM_MANAGEMENT],
    },
    {
      components: <BulkOperationsTracker user={user} />,
      permission: [ROUTE_PERMISSION?.VIEW_TRACE_DATA],
      repository: [REPOSITORY?.AUDIT],
    },
    {
      components: <SimOrdering user={user} />,
      permission: [ROUTE_PERMISSION?.VIEW_ORDERS],
      repository: [REPOSITORY?.SIM_ORDERS],
    },
  ];

  return (
    <div
      className='sim-management-page'
      data-testid="sim-management-page"
      style={{
        background: styles.lightColor50
      }}
    >
      <Box
        sx={{
          '.MuiTabs-flexContainer': {
            '.Mui-disabled': {
              display: 'none',
            },
          },
        }}
      >
        <Tabs
          tabIndex={tabIndex}
          setTabIndex={setTabIndex}
          selectedNewTab={selectedNewTab}
          selectedTab={tabIndex}
          primaryColor={theme?.palette?.secondary?.main}
          tabItemsConfig={tabItemsConfigs}
        />
        {
          TabComponent?.map((item, index) => (
            <TabPanel index={index} value={tabIndex}>
              <FadeIn>
                <CommonAuthwrapper
                  permission={item?.permission}
                  repository={item?.repository}
                  isComponent
                >
                  {item?.components}
                </CommonAuthwrapper>
              </FadeIn>
            </TabPanel>
          ))
        }
      </Box>
    </div>
  );
};

export default SimManagement;
