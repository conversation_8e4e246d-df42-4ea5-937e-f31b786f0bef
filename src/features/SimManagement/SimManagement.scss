.sim-management-page {
  padding: 20px;

  .MuiTab-root {
    span.tabs-tab-component__title {
      font-size: 14px;
      color: #333 !important;
    }

    svg {
      color: #333 !important;
    }

    svg path {
      stroke: #333;
    }

    &:hover svg {
      color: #333 !important;
    }
  }

  &__back {
    margin-right: 15px;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: none;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease-out 100ms;

    &:hover {
      background: var(--light-background, #f5f1fa);
    }
  }
}

@media screen and (max-width: 1600px) {
  .sim-management-page {
    .tabs-tab-component__title {
      font-size: 13px;
    }
  }
}
