import {
  Autocomplete,
  Box,
  Grid,
  <PERSON><PERSON><PERSON>utton,
  TextField,
  Typography
} from "@mui/material";
import { makeStyles } from "@mui/styles";
import { Form, Formik, getIn } from "formik";
import React from "react";
import { AiOutlineMinusCircle, AiOutlinePlusCircle } from "react-icons/ai";
import { GrFormDown } from "react-icons/gr";
import { countryOptions, simTypeObj } from "./constant";
import TextFieldWrapper from "./TextFieldWrapper";
import { formFactor } from "../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants";
import { useAppContext } from "AppContextProvider";

const OrderNewSimForm = ({ formik, initialValues, validationSchema, handleSubmit, user }) => {

  const { currentTheme } = useAppContext();


  const useStyles = makeStyles({
    DropDown: {
      width: "auto",
      "& .MuiAutocomplete-endAdornment": {
        top: "calc(50% - 1px)",
        right: 0,
      },
      "& .MuiFormHelperText-root": {
        marginLeft: 4,
        fontSize: "12px",
      },
    },
    textField: {
      "& .MuiInputLabel-root": {
        transform: "translate(15px, 9px) scale(1)",
        backgroundColor: '#ffffff',
      },
      "& .MuiFormHelperText-root": {
        marginLeft: 4,
        fontSize: "12px",
      },
      "& .Mui-readOnly": {
        color: "red",
      },
    },
    multiLineTextField: {
      "& .MuiOutlinedInput-root": {
        padding: "9.5px 14px",
      },
      "& .MuiInputLabel-root": {
        transform: "translate(13px, 10px) scale(1)",
        backgroundColor: '#ffffff',
      },
      "& .MuiFormHelperText-root": {
        marginLeft: 0,
        fontSize: "12px",
      },
    },
  });
  const classes = useStyles();

  return (
    <Box sx={{ px: "20px" }}>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        <Form>
          <Typography
            color="primary.main"
            variant="h3"
            component="h3"
            mb={"32px"}
          >
            Customer Details
          </Typography>
          <Grid container spacing={3} mb={"60px"}>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="customerDetails.customerReference"
                label="Customer Account Reference"
                formikValue={formik}
                disabled
                error={!!formik.errors?.customerDetails?.customerReference}
                helperText={
                  formik.touched?.customerDetails?.customerReference &&
                    formik.errors?.customerDetails?.customerReference
                    ? formik.errors?.customerDetails?.customerReference
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="customerDetails.personPlacingOrder"
                label="Name of person placing order"
                formikValue={formik}
                disabled={(user?.firstName || user?.lastName)}
                error={!!formik.errors?.customerDetails?.personPlacingOrder}
                helperText={
                  formik.touched?.customerDetails?.personPlacingOrder &&
                    formik.errors?.customerDetails?.personPlacingOrder
                    ? formik.errors?.customerDetails?.personPlacingOrder
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="customerDetails.customerEmail"
                label="Email of person placing order"
                formikValue={formik}
                disabled
                error={!!formik.errors?.customerDetails?.customerEmail}
                helperText={
                  formik.touched?.customerDetails?.customerEmail &&
                    formik.errors?.customerDetails?.customerEmail
                    ? formik.errors?.customerDetails?.customerEmail
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="customerDetails.customerContactNo"
                label="Phone number of person placing order"
                pattern={new RegExp("^[\\d+\\-()\\s]*$")}
                formikValue={formik}
                error={!!formik.errors?.customerDetails?.customerContactNo}
                helperText={
                  formik.touched?.customerDetails?.customerContactNo &&
                    formik.errors?.customerDetails?.customerContactNo
                    ? formik.errors?.customerDetails?.customerContactNo
                    : ""
                }
              />
            </Grid>
          </Grid>

          {/* Shipping Details Section */}
          <Typography
            color="primary.main"
            variant="h3"
            component="h3"
            mb={"32px"}
          >
            Shipping Details
          </Typography>
          <Grid container spacing={3} mb={"60px"}>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.contactName"
                label="Customer Contact Name"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.contactName}
                helperText={
                  formik.touched?.shippingDetails?.contactName &&
                    formik.errors?.shippingDetails?.contactName
                    ? formik.errors?.shippingDetails?.contactName
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.addressLine1"
                label="Address Line 1"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.addressLine1}
                helperText={
                  formik.touched?.shippingDetails?.addressLine1 &&
                    formik.errors?.shippingDetails?.addressLine1
                    ? formik.errors?.shippingDetails?.addressLine1
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.addressLine2"
                label="Address Line 2"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.addressLine2}
                helperText={
                  formik.touched?.shippingDetails?.addressLine2 &&
                    formik.errors?.shippingDetails?.addressLine2
                    ? formik?.errors.shippingDetails?.addressLine2
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <Autocomplete
                fullWidth
                size="medium"
                options={countryOptions || []}
                value={
                  countryOptions?.find(
                    (option) =>
                      option.value ===
                      getIn(formik.values, "shippingDetails.country")
                  ) || null
                }
                onChange={(event, newValue) =>
                  formik.setFieldValue(
                    "shippingDetails.country",
                    newValue ? newValue.value : ""
                  )
                }
                onBlur={() =>
                  formik.setFieldTouched("shippingDetails.country", true)
                }
                getOptionLabel={(option) => option.label || ""}
                isOptionEqualToValue={(option, value) =>
                  option.value === value?.value
                }
                popupIcon={<GrFormDown size={18} />}
                className={`${classes.DropDown} autocomplete-wrapper`}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Country"
                    error={
                      !!(
                        formik.touched.shippingDetails?.country &&
                        formik.errors.shippingDetails?.country
                      )
                    }
                    helperText={
                      formik.touched.shippingDetails?.country &&
                        formik.errors.shippingDetails?.country
                        ? formik.errors.shippingDetails.country
                        : ""
                    }
                    fullWidth
                    size="medium"
                    InputLabelProps={{
                      className: params.InputProps.className || "",
                    }}
                    InputProps={{
                      ...params.InputProps,
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.stateOrRegion"
                label="State / Region"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.stateOrRegion}
                helperText={
                  formik.touched?.shippingDetails?.stateOrRegion &&
                    formik.errors?.shippingDetails?.stateOrRegion
                    ? formik.errors?.shippingDetails?.stateOrRegion
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.city"
                label="City"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.city}
                helperText={
                  formik.touched?.shippingDetails?.city &&
                    formik.errors?.shippingDetails?.city
                    ? formik.errors?.shippingDetails?.city
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4} md={3} xl={3}>
              <TextFieldWrapper
                className={classes.textField}
                name="shippingDetails.postalCode"
                label="Postal Code"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.postalCode}
                helperText={
                  formik.touched?.shippingDetails?.postalCode &&
                    formik.errors?.shippingDetails?.postalCode
                    ? formik.errors?.shippingDetails?.postalCode
                    : ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={6} md={6} xl={6}>
              <TextFieldWrapper
                className={classes.multiLineTextField}
                name="shippingDetails.otherInformation"
                label="Any other information"
                formikValue={formik}
                error={!!formik.errors?.shippingDetails?.otherInformation}
                multiline
                maxRows={4}
                helperText={
                  formik.touched?.shippingDetails?.otherInformation &&
                    formik.errors?.shippingDetails?.otherInformation
                    ? formik.errors?.shippingDetails?.otherInformation
                    : ""
                }
              />
            </Grid>
          </Grid>

          {/* SIM Type & Quantity Section */}
          <Typography
            color="primary.main"
            variant="h3"
            component="h3"
            mb={"32px"}
          >
            SIM Type & Quantity
          </Typography>
          <Box mb={"60px"}>
            {formik.values?.orderItems?.map((item, inx) => (
              <Grid container spacing={3} mb={"24px"}>
                <Grid item xs={6} sm={3}>
                  <Autocomplete
                    fullWidth
                    size="medium"
                    options={
                      formFactor?.filter(
                        (x) =>
                          !formik.values?.orderItems
                            ?.map((y) => y.simType)
                            .includes(x.value)
                      ) || []
                    }
                    value={
                      formFactor?.find(
                        (option) =>
                          option.value ===
                          getIn(formik.values, `orderItems.${inx}.simType`)
                      ) || null
                    }
                    onChange={(event, newValue) =>
                      formik.setFieldValue(
                        `orderItems.${inx}.simType`,
                        newValue ? newValue.value : ""
                      )
                    }
                    onBlur={() =>
                      formik.setFieldTouched(
                        `orderItems.${inx}.simType`,
                        true
                      )
                    }
                    getOptionLabel={(option) => option.title || ""}
                    isOptionEqualToValue={(option, value) =>
                      option.value === value?.value
                    }
                    popupIcon={<GrFormDown size={18} />}
                    className={`${classes.DropDown} autocomplete-wrapper`}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="SIM Type"
                        error={
                          !!(
                            getIn(
                              formik.touched,
                              `orderItems.${inx}.simType`
                            ) &&
                            getIn(formik.errors, `orderItems.${inx}.simType`)
                          )
                        }
                        helperText={
                          getIn(
                            formik.touched,
                            `orderItems.${inx}.simType`
                          ) &&
                            getIn(formik.errors, `orderItems.${inx}.simType`)
                            ? getIn(
                              formik.errors,
                              `orderItems.${inx}.simType`
                            )
                            : ""
                        }
                        fullWidth
                        size="medium"
                        InputLabelProps={{
                          className: params.InputProps.className || "",
                        }}
                        InputProps={{
                          ...params.InputProps,
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={6} sm={2}>
                  <TextFieldWrapper
                    className={classes.textField}
                    name={`orderItems.${inx}.quantity`}
                    label="Qty"
                    type="Number"
                    inputProps={{
                      inputMode: "numeric",
                      pattern: "[0-9]*",
                      onKeyDown: (e: React.KeyboardEvent) => {
                        if (["-", "+", ".", "e"].includes(e.key)) {
                          e.preventDefault();
                        }
                      },
                    }}
                    formikValue={formik}
                    error={!!(formik.touched?.orderItems?.[inx]?.quantity && formik.errors?.orderItems?.[inx]?.quantity)}
                    helperText={
                      formik.touched?.orderItems?.[inx]?.quantity &&
                        formik.errors?.orderItems?.[inx]?.quantity
                        ? formik?.errors.orderItems?.[inx]?.quantity
                        : ""
                    }
                  />
                </Grid>
                <Grid item xs={1}>
                  <Box sx={{ display: "flex" }}>
                    <IconButton
                      onClick={(e) => {
                        e.preventDefault();
                        const currentVal: Array<any> =
                          formik.values.orderItems;
                        formik.setFieldValue("orderItems", [
                          ...currentVal,
                          simTypeObj,
                        ]);
                      }}
                      color="primary"
                      disabled={
                        formFactor.length ===
                        formik.values?.orderItems.length ||
                        formik.errors.orderItems || !formik.dirty
                      }
                    >
                      <AiOutlinePlusCircle />
                    </IconButton>
                    {inx > 0 && (
                      <IconButton
                        onClick={(e) => {
                          e.preventDefault();
                          const currentVal: Array<any> =
                            formik.values.orderItems;
                          currentVal.splice(inx, 1);
                          formik.setFieldValue("orderItems", currentVal);
                        }}
                        color="primary"
                      >
                        <AiOutlineMinusCircle />
                      </IconButton>
                    )}
                  </Box>
                </Grid>
              </Grid>
            ))}
          </Box>
          <Box
            sx={{
              p: "10px",
              backgroundColor: currentTheme?.bgLightPrimary ||"#f5f1fa",
              display: "inline-block",
              mb: "24px",
            }}
          >
            <Typography variant="body2" color="#000000">
              <b>Please Note:</b> For MFF2 SIMs minimum order is 1000.
            </Typography>
          </Box>
        </Form>
      </Formik>
    </Box>
  );
};

export default OrderNewSimForm;
