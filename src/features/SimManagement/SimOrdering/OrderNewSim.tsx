import React, { useEffect, useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import { Formik } from 'formik';
import { useNavigate } from 'react-router-dom';
import Loader from 'shared/Loader';
import TopBar from 'shared/TopBar';
import * as Yup from 'yup';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import './orderNewSim.scss';
import OrderNewSimForm from './OrderNewSimForm';
import { createSimOrder, getAccountDetails } from '../api.service';
import { setAppRoute } from 'core/utilities/checkRoutes';
import ConfirmOrderDetailsModal from './ConfirmOrderDetailsModal';

const validationSchema = Yup.object().shape({
  // Customer Details validation
  customerDetails: Yup.object().shape({
    customerReference: Yup.string(),
    personPlacingOrder: Yup.string().required("Person placing order is required").max(60, "Person placing order cannot exceed 60 characters"),
    customerAccountId: Yup.string(),
    customerAccount_logo_url: Yup.string(),
    customerId: Yup.string(),
    customerEmail: Yup.string()
      .email("Invalid email format")
      .required("Email is required"),
    customerContactNo: Yup.string()
      .required("Phone number is required"),
  }),
  // Shipping Details validation
  shippingDetails: Yup.object().shape({
    contactName: Yup.string().required("Contact name is required").max(60, "Contact name cannot exceed 60 characters"),
    addressLine1: Yup.string().required("Address line 1 is required"),
    addressLine2: Yup.string(),
    city: Yup.string().required("City is required"),
    stateOrRegion: Yup.string().required("State/Region is required"),
    postalCode: Yup.string().required("Postal code is required"),
    country: Yup.string().required("Country is required"),
    otherInformation: Yup.string().max(255, "Other information cannot exceed 255 characters"),
  }),
  // Order Items validation
    orderItems: Yup.array().of(
    Yup.object().shape({
      simType: Yup.string().required("SIM type is required"),
      quantity: Yup.number()
        .typeError("Quantity must be a number")
        .positive("Quantity must be more than zero")
        .integer("Quantity must be an integer")
        .required("Quantity is required")
        .test(
          'min-quantity-for-esim',
          'Minimum quantity for MFF2 is 1000',
          function(value) {
            const simType = this.parent.simType;
            if ((simType === 'eSIM_MFF2' || simType === 'eSIM_MFF2_eUICC') && Number(value) < 1000) {
              return false;
            }
            return true;
          }
        )
    })
  ).min(1, "At least one order item is required"),

});

const OrderNewSim = ({ user }) => {
  const [loading, setLoading] = useState(false);
  const [formLoader, setFormLoader] = useState(false);
  const [openConfirmationModel, setOpenConfirmationModel] = useState(false);
  const [initialValues, setInitialValues] = useState({
    customerDetails: {
      customerReference: '',
      personPlacingOrder: '',
      customerAccountId: '',
      customerAccountLogoUrl: '',
      customerId: '',
      customerEmail: '',
      customerContactNo: '',
      customerAccountName: '',
    },
    shippingDetails: {
      contactName: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      stateOrRegion: '',
      postalCode: '',
      country: '',
      otherInformation: '',
    },
    orderItems: [
      {
        simType: '',
        quantity: '',
      }
    ],
    orderBy: '',
  });
  const navigate = useNavigate();

  const handleSubmit = () => {
    setOpenConfirmationModel(true);
  }

  const handleSubmitForm = async (values) => {
    try {
      setFormLoader(true);
      const orderPayload = {
        customerDetails: {
          ...values.customerDetails,
        customerAccountId: Number(values.customerDetails.customerAccountId),
        },
        shippingDetails: values.shippingDetails,
        orderItems: values.orderItems.map(item => ({...item, quantity: Number(item.quantity)})),
        orderBy: values.orderBy,
      };
      await createSimOrder(orderPayload);      
      toastSuccess('Order Place Successfully!');
      navigate(setAppRoute('/?tab=sim-order&page=1&pageSize=10'));
    } catch (error: any) {
      if (error && error?.response?.status === 422) {
        toastError(`Failed to submit SIM order: ${error?.detail?.msg}`);
      };
      toastError('Failed to submit SIM order');
    } finally {
      setFormLoader(false);
    }
  };

  const getUserDetails = async () => {
    const userLocal = JSON.parse(localStorage.getItem('userDetails') || 'null');
    const response = await getAccountDetails(userLocal?.accountId);
    return response?.data;
  }

  useEffect(() => {
    setLoading(true);
    const fetchUserDetails = async () => {
      try {
        const userDetails = await getUserDetails();
        setInitialValues({
          customerDetails: {
            personPlacingOrder: `${user?.firstName || ''} ${user?.lastName || ''}`.trim() || '',
            customerReference: userDetails?.name || '',
            customerAccountName: userDetails?.name || '',
            customerAccountId: String(userDetails?.id) || '',
            customerAccountLogoUrl: userDetails?.logoUrl || '',
            customerId: user?.id || '',
            customerEmail: user?.email || '',
            customerContactNo: userDetails?.phone || '',
          },
          shippingDetails: {
            contactName: userDetails?.contactName || '',
            addressLine1: userDetails?.address1 || '',
            addressLine2: userDetails?.address2 || '',
            city: userDetails?.city || '',
            stateOrRegion: userDetails?.stateRegion || '',
            postalCode: userDetails?.postcode || '',
            country: userDetails?.country || '',
            otherInformation: '',
          },
          orderItems: [
            {
              simType: '',
              quantity: '',
            }
          ],
          orderBy: user?.email,
        });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toastError('Failed to load user details');
        navigate(setAppRoute('/?tab=sim-order&page=1&pageSize=10'));
      } finally {
        setLoading(false);
      }
    };    
    fetchUserDetails();
  }, []);
  return (
    <Box
      sx={{ flexGrow: 1 }}
      className="customer-account-page"
      data-testid="customer-account-page"
    >
      <Formik
        initialValues={initialValues}
        enableReinitialize
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {(formik) => (
          <>
            <TopBar className="customer-account-page_header">
              <Box sx={{ flexGrow: 1 }} display="flex" alignItems="center">
                <Typography
                  data-testid="customer-account-page_title"
                  className="customer-account-page_title"
                  variant="h3"
                  component="h3"
                >
                  Order New SIM(s)
                </Typography>
              </Box>
              <Button
                className="customer-account-page_button"
                data-testid="customer-account-page_button"
                variant="contained"
                size="large"
                color="primary"
                onClick={() => formik.handleSubmit()}
                disabled={!(formik.isValid && formik.dirty)}
              >
                Place order
              </Button>
            </TopBar>
            {!loading && (
              <OrderNewSimForm
                formik={formik}
                initialValues={formik.values}
                validationSchema={validationSchema}
                handleSubmit={handleSubmit}
                user={user}
              />
            )}
            {loading && (
              <div className="customer-account-page__loader">
                <Loader staticColor="#f5f1fa" size={60} />
              </div>
            )}
            {openConfirmationModel && (
              <ConfirmOrderDetailsModal
                loading={formLoader}
                formValues={formik}
                open={openConfirmationModel}
                setOpen={setOpenConfirmationModel}
                handleSubmitForm={handleSubmitForm}
              />
            )}
          </>
        )}
      </Formik>
    </Box>
  );
};

export default OrderNewSim;
