.drawer-header {
  box-shadow: #895ccc26 0 6px 16px;
  color: white;
  transition: all 0.3s ease-in-out 25ms;
  padding: 20px 15px;

  .drawer-header-title {
    font-weight: 500;
    font-size: 18px;
    line-height: 22px;
    font-family: "Open Sans", sans-serif;
  }
}

.customAutoComplete {
  .MuiOutlinedInput-adornedEnd {
    padding: 2px !important;

    .MuiAutocomplete-endAdornment {
      top: 50% !important;
    }

    .MuiInputLabel-shrink {
      transform: "translate(14px, -8px) scale(0.7) !important";
    }
  }
}

.volumeClass {
  width: 50%;
}

.textField {
  margin-top: 24px !important;

  .MuiInputBase-root {
    height: 40px;
  }

  .MuiInputLabel-outlined {
    transform: translate(14px, 9px) scale(1);
  }
}

.dialog-body {
  margin-top: 32px;
  margin-left: 24px;
  margin-right: 20px;
}

.value-text {
  font-weight: 700 !important;
  font-size: 14px !important;
  text-align: left !important;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 100%;
  hyphens: auto;
}

.title-text {
  font-weight: 400 !important;
  font-size: 14px !important;
  text-align: left !important;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  max-width: 100%;
  hyphens: auto;
}

body {
  background-color: $app-background-color;
  font-size: 14px;

  .scrollBar::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .scrollBar::-webkit-scrollbar-thumb {
    // box-shadow: inset 0 0 6px rgba(163, 163, 177, 0.9);
    background-color: #dedee6;
    padding-inline: 2px;
    border-radius: 20px;
  }
}

body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.black_stroke {
  svg {
    stroke: #000;
    fill: #000;
  }
}

body::-webkit-scrollbar-thumb {
  // box-shadow: inset 0 0 6px rgba(163, 163, 177, 0.9);
  background-color: #c4c4ce;
  padding-inline: 2px;
  border-radius: 20px;
}
