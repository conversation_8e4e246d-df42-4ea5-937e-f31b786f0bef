import { Box, Button, Tooltip } from '@mui/material';
import MuiTable from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable';
import { MuiTableProvider } from '@nv2/nv2-pkg-js-shared-components/lib/MuiTable/MuiTableContext';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import { useAppContext } from 'AppContextProvider';
import { GetAuthorization } from 'PrivateRotes';
import SimIconHeader from 'assets/images/SimIconHeader';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import useAbortController from 'core/hooks/useAbortController';
import { setAppRoute } from 'core/utilities/checkRoutes';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import { TGetCurrentThemeColors } from 'features/models';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import useSimManagementClientSearchParams from 'hooks/useSimManagementClientSearchParams';
import useBulkOperationsTrackerColumns from 'hooks/useSimOrderingColumns';
import React, { useEffect, useState } from 'react';
import { AiOutlineShoppingCart } from 'react-icons/ai';
import { useLocation, useNavigate } from 'react-router-dom';
import { IOrderDetails, SimOrderingInterface } from '../SimManagement.models';
import SimManagementClientTableActionsLeft from '../SimManagementClient/SimManagementClientTableActionsLeft';
import { simOrderingList } from '../api.service';
import SimDetailsDrawer from './SimDetailsDrawer';
import './SimOrdering.scss';
import { descending } from './constant';

const SimOrdering = ({ user }: SimOrderingInterface) => {
  const { primaryColor, getBrandColors } = useAppContext();
  const {
    setParamsToUrl,
    getParamsFromUrl,
    defaultPagination,
    defaultSort,
    initialSearchValue,
  } = useSimManagementClientSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const [confirmDialog, setConfirmDialog] = useState<{
    isEnable: boolean;
    rowData: IOrderDetails | null;
  }>({
    isEnable: false,
    rowData: null,
  });
  const onSimOrderingActionClick = (event, row) => {
    event.stopPropagation();
    setConfirmDialog({ isEnable: true, rowData: row });
  };
  const { columns } = useBulkOperationsTrackerColumns({ onSimOrderingActionClick, user });
  const [simOrders, setSimOrders] = useState<any[]>([]);
  const [cardsTotalCount, setCardsTotalCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);
  const { generateParamsForUrl } = useMuiTableSearchParams();

  const loadCards = async (page, pageSize, field, sort, search) => {
    cancelPreviousRequest();
    const { signal } = setNewController();
    const newSearchParams = generateParamsForUrl(
      page, pageSize, field, sort, search,
    );
    const newUrl = `${location?.pathname
      }?tab=sim-order&${newSearchParams?.toString()}`;
    navigate(newUrl, { replace: true });

    let ordering = field; //sortFieldNames[field];

    if (sort === descending) {
      ordering = `-${ordering}`;
    }

    let userProfile = JSON.parse(localStorage.getItem('userDetails') || 'null');

    const { data: { results: OrderListData, totalCount } } = await simOrderingList(page, pageSize, ordering, search, signal, user?.organization.type === 'CLIENT' ? userProfile?.accountId : null);

    setSimOrders(OrderListData);
    setCardsTotalCount(totalCount);
  };
  const OrderSimButton = () => (
    <CommonAuthwrapper permission={[ROUTE_PERMISSION?.CREATE_ORDER]} repository={[REPOSITORY?.SIM_ORDERS]}>
      <Tooltip title={`ORDER SIM(s)`} arrow placement='top'>
        <Button
          disabled={false}
          onClick={(e) => {
            e.preventDefault();
            navigate(setAppRoute('/sim-order-form'));
          }}
          component="button"
          sx={{
            p: "0px 25px",
            minWidth: "110px",
            textTransform: "uppercase",
            fontSize: "12px !important",
          }}
          variant="contained"
          color="primary"
          startIcon={<AiOutlineShoppingCart />}
          onMouseDown={(e) => e.preventDefault()}
          data-testid="create-allocation-modal__body_buttons_confirm"
        >
          {`ORDER SIM(s)`}
        </Button>
      </Tooltip>
    </CommonAuthwrapper>
  );

  const loadData = async (page, pageSize, field, sort, search) => {
    setParamsToUrl(page, pageSize, field, sort, search);
    try {
      setIsLoading(true);
      await loadCards(page, pageSize, field, sort, search);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (errorObj: any) {
      setCardsTotalCount(0);
      setSimOrders([]);
    } finally {
      setIsLoading(false);
    }
  };

  const noDataOrdering = {
    icon: <SimIconHeader />,
    title: "No Orders Found",
    description: GetAuthorization(
      [ROUTE_PERMISSION?.CREATE_ORDER],
      [REPOSITORY?.SIM_ORDERS]
    ) ? (
      "You can request a new order by click on below button."
    ) : (
      <div style={{ textAlign: 'center' }}>
        <div>
          Once your customer places a new order, the details will appear here.
        </div>
        <div>Please check back later.</div>
      </div>
    ),
    additionalItem: (
      <Box sx={{ py: "16px" }}>
        <OrderSimButton />
      </Box>
    ),
  };

  const onChange = ({ page, pageSize }, { field, sort }, search) => {
    loadData(page, pageSize, field, sort, search);
  };
  const getApiData = () => {
    const {
      page, pageSize, field, sort, search,
    } = getParamsFromUrl();

    loadData(page, pageSize, field, sort, search);
  };

  useEffect(() => {
    getApiData();
  }, []);

  return (
    <Box
      data-testid="sim-management-client"
      sx={{
        background: styles.lightColor50,
        "& .MuiDataGrid-row-bold": {
          "& .MuiCheckbox-root": {
            display: "none !important",
          },
          "& .MuiDataGrid-main": {
            overflow: "hidden !important",
          },
          ":hover": {
            cursor: "default !important",
          },
        },
        "& .MuiDataGrid-columnHeaders": {
          backgroundColor: "#F5F5F9 !important",
        },
        "& .MuiDataGrid-virtualScrollerContent": {
          minHeight: "160px !important",
        },
        "& .MuiDataGrid-columnHeaderTitleContainerContent": {
          "& .MuiCheckbox-root": {
            display: "none !important",
          },
        },
        "& .interactions__actions": {
          justifyContent: "flex-start",
          "& .interactions__actions-selections": {
            display: "none",
          },
        },
      }}
    >
      <div>
        <MuiTableProvider
          defaultPagination={defaultPagination}
          onChange={onChange}
          onChangePagination={onChange}
          onChangeSearch={onChange}
          initialSearchValue={initialSearchValue}
          onChangeSort={onChange}
          defaultSort={defaultSort}
        >
          <MuiTable
            sx={{
              "& .MuiDataGrid-row:hover": {
                cursor: "pointer",
                "& .MuiButtonBase-root": {
                  visibility: "visible ",
                },
              },
            }}
            Actions={() => (
              <Box
                display="flex"
                justifyContent="space-between"
                width="100%"
                flexDirection="row-reverse"
              >
                {cardsTotalCount > 0 && (
                  <CommonAuthwrapper permission={[ROUTE_PERMISSION?.CREATE_ORDER]} repository={[REPOSITORY?.SIM_ORDERS]}>
                    <OrderSimButton />
                  </CommonAuthwrapper>
                )}
              </Box>
            )}
            ActionsLeft={SimManagementClientTableActionsLeft}
            rows={simOrders}
            columns={columns}
            loading={isLoading}
            rowCount={cardsTotalCount}
            primaryColor={primaryColor as string}
            getCurrentThemeColors={getBrandColors as TGetCurrentThemeColors}
            showFirstLastPageButtons
            isVisibleSearchInput
            getRowId={(row) => `${row?.orderId}`}
            hideFooter={!cardsTotalCount}
            noDataConfig={noDataOrdering}
          />
        </MuiTableProvider>
        <SimDetailsDrawer
          open={confirmDialog.isEnable}
          orderDetails={confirmDialog.rowData}
          onClose={() => setConfirmDialog({ isEnable: false, rowData: null })}
          listFunction={getApiData}
          user={user}
        />
      </div>
    </Box>
  );
};

export default SimOrdering;
