import React, { useEffect, useRef, useState } from "react";
import { Formik } from "formik";
import * as Yup from "yup";
import CommonDrawer from "@nv2/nv2-pkg-js-shared-components/lib/CommonDrawer";
import {
  Autocomplete,
  Box,
  Button,
  Grid,
  Typography,
  TextField,
  Skeleton,
} from "@mui/material";
import { CloseOutlined } from "@mui/icons-material";
import { GrFormDown } from "react-icons/gr";
import { getIn } from "formik";
import "./SimDetailsDrawer.scss";
import StatusText from "shared/Status/StatusText";
import dayjs from "dayjs";
import { IDetailsItemProps, ISimDetailsFormValues, orderDetailsProps, orderStatusChange } from "../SimManagement.models";
import TextFieldWrapper from "./TextFieldWrapper";
import { makeStyles } from "@mui/styles";
import { countryOptions, orderStatusDisplay, orderStatusOptions, orderStatusOptionsClient } from "./constant";
import { LoadingButton, TimelineDot } from "@mui/lab";
import { changeSimOrderStatus, getSimOrderDetails } from "../api.service";
import { toastError, toastSuccess } from "core/utilities/toastHelper";
import { capitalizeFirstLetter, REPOSITORY, ROUTE_PERMISSION, simOrderStatusColorMap } from "core/utilities/constants";
import { useCopyWithCooldown } from "hooks/useCopyWithCooldown ";
import { GetAuthorization } from "PrivateRotes";
import { formFactor } from "../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants";
import { useAppContext } from "AppContextProvider";

const DetailsItem = ({ label, value, customSx, loader }: IDetailsItemProps) => (
  <Grid
    container
    sx={{
      mt: 5,
      borderBottom: "1px solid #DEDEE6",
      pb: 2,
      ...customSx,
    }}
  >
    {loader ? (
      <Skeleton data-testid="skeleton" width="100%" height="48px" />
    ) : (
      <>
        <Grid item xs={6}>
          <Typography className="title-text">{label}</Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography className="value-text">{value}</Typography>
        </Grid>
      </>
    )}
  </Grid>
);

const SimDetailsDrawer = ({
  user,
  open,
  onClose,
  orderDetails,
  listFunction,
}: orderDetailsProps) => {
  const statusColor = simOrderStatusColorMap();
  const { currentTheme } = useAppContext()
  const { handleCopy } = useCopyWithCooldown(5000);
  const [simOrderDetails, setSimOrderDetails] = useState<any>({});
  const [loader, setLoader] = useState<boolean>(false);
  const [editableForm, setEditableForm] = useState<orderStatusChange>({
    isChange: false,
    val: "",
  });
  const [initialFormValues, setInitialFormValues] =
      useState<ISimDetailsFormValues>({
      order_status: "",
      tracking_url: "",
      tracking_reference: "",
      reject_reason: "",
    });
  const useStyles = makeStyles({
    DropDown: {
      width: "auto",
      "& .MuiAutocomplete-endAdornment": {
        top: "calc(50% - 1px)",
        right: 0,
      },
      "& .MuiFormHelperText-root": {
        marginLeft: 4,
        fontSize: "12px",
      },
    },
    textField: {
      "& .MuiInputLabel-root": {
        transform: "translate(16px, 12px) scale(1)",
        paddingRight: "4px",
        backgroundColor: "white",
      },
      "& .MuiFormHelperText-root": {
        marginLeft: 4,
        fontSize: "12px",
      },
      "& .Mui-readOnly": {
        color: "red",
      },
    },
  });

  const classes = useStyles();
  const isAuthorize = GetAuthorization([ROUTE_PERMISSION?.UPDATE_ORDER_STATUS], [REPOSITORY?.SIM_ORDERS]);
  const isDistributor = user?.organization?.type === 'DISTRIBUTOR';
  const inputRef1 = useRef(null);
  const inputRef2 = useRef(null);

  // Validation schema
  const validationSchema = Yup.object().shape({
    order_status: Yup.string().when([], {
      is: () => isAuthorize,
      then: Yup.string().required("Order status is required"),
      otherwise: Yup.string(),
    }),
    tracking_url: Yup.string().when("order_status", {
      is: "SHIPPED",
      then: Yup.string()
        .url("Please enter a valid URL, eg: https://www.tracking.com")
        .required("Tracking URL is required when order is shipped"),
      otherwise: Yup.string().when("order_status", {
        is: "Rejected",
        then: Yup.string(),
        otherwise: Yup.string().nullable(),
      }),
    }),
    tracking_reference: Yup.string().when("order_status", {
      is: "SHIPPED",
      then: Yup.string().required(
        "Tracking reference is required when order is shipped"
      ),
      otherwise: Yup.string().nullable(),
    }),
    reject_reason: Yup.string().when("order_status", (order_status, schema) => {
      if (["CANCELLED", "ONHOLD"].includes(order_status)) {
        return schema.required(`Reason is required when order is ${capitalizeFirstLetter(order_status)}`);
      }
      return schema.nullable();
    }),
  });

  const handleFormSubmit = async (values: ISimDetailsFormValues) => {
    if (!isAuthorize || values.order_status === editableForm.val) {
      onClose();
      return;
    }
    setLoader(true);
    try {
      switch (values.order_status) {
        case "SHIPPED":
          await changeSimOrderStatus(orderDetails?.orderUuid, {
            status: values.order_status,
            tracking: {
              referenceId: values.tracking_reference,
              referenceUrl: values.tracking_url,
            }
          });
          break;

        case "CANCELLED":
          await changeSimOrderStatus(orderDetails?.orderUuid, {
            status: values.order_status,
            comments: values.reject_reason,
          });
          break;

        case "ONHOLD":
          await changeSimOrderStatus(orderDetails?.orderUuid, {
            status: values.order_status,
            comments: values.reject_reason,
          });
          break;

        case "APPROVED":
          await changeSimOrderStatus(orderDetails?.orderUuid, {
            status: values.order_status,
          });
          break;

        case "PENDING":
          setLoader(false);
          onClose();
          return;

        default:
          return;
      }
      toastSuccess("Order status updated successfully");
      listFunction(1, 10, '-orderDate');
      setLoader(false);
      onClose();
    } catch (error: any) {
      setLoader(false);
      console.error("Error updating order:", error);
      toastError(error?.response?.data?.detail || "Failed to update order");
    }
  };

  useEffect(() => {
    if (!orderDetails?.orderId) return;
    setLoader(true);
    const getSimDetails = async () => {
      try {
        const { data: res } = await getSimOrderDetails(orderDetails?.orderUuid);
        setSimOrderDetails(res);
        setInitialFormValues({
          order_status: res?.status,
          tracking_url: res?.orderTracking?.referenceUrl,
          tracking_reference: res?.orderTracking?.referenceId,
          reject_reason: res?.comments || '',
        });
        if (isDistributor) {
          setEditableForm({
            isChange: !!(res?.status === 'SHIPPED' || res?.status === 'CANCELLED'),
            val: res?.status,
          });
        } else {
          setEditableForm({
            isChange: !!(res?.status !== 'PENDING' || res?.status === 'ONHOLD'),
            val: res?.status,
          });
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
      } catch (error) {
        toastError("Failed to load SIM order details");
        onClose();
      } finally {
        setLoader(false);
      }
    };
    getSimDetails();
  }, [orderDetails?.orderId]);

  const handleCopyUrl = (ref) => {
    handleCopy(ref, () => toastSuccess("Copied to clipboard!"));
  };

  return (
    <CommonDrawer
      onClose={onClose}
      open={open}
      anchor="right"
      variant="temporary"
    >
      <Formik<ISimDetailsFormValues>
        initialValues={{
          ...initialFormValues,
          order_status: orderDetails?.status || "",
        }}
        enableReinitialize
        validationSchema={validationSchema}
        onSubmit={handleFormSubmit}
      >
        {(formik) => {
          const drawerButtonCondition = !editableForm.isChange && (formik.values.order_status !== editableForm.val) && (formik.values.order_status !== 'PENDING') && isAuthorize;
          const { errors, submitCount } = formik;
          if (submitCount > 0 && Object.keys(errors).length > 0) {
            const firstErrorKey = Object.keys(errors)[0];
            const field = document.getElementById(firstErrorKey);
            if (field && typeof field.focus === "function") {
              if (
                field.scrollIntoView &&
                typeof field.scrollIntoView === "function"
              ) {
                field.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            }
          }
          return (
            <form onSubmit={formik.handleSubmit}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                background: currentTheme?.primaryBgGradient
                }}
                className={`drawer-header`}
              >
                <div>
                  <h3 className="drawer-header-title">
                    Order Summary
                  </h3>
                </div>
                <CloseOutlined
                  style={{ fontSize: "28px" }}
                  onClick={() => {
                    onClose();
                  }}
                />
              </Box>
              <Box
                className="scrollBar"
                sx={{
                  p: '24px',
                  flex: 1,
                  overflowY: "auto",
                  maxHeight: "calc(100vh - 150px)",
                  minHeight: "calc(100vh - 150px)",
                }}
              >
                <Typography variant="subtitle1" fontWeight="bold">
                  Order Details
                </Typography>
                <DetailsItem label="Order ID" value={orderDetails?.orderId} />
                {!editableForm.isChange && isAuthorize ? (
                  <DetailsItem
                    customSx={{ alignItems: "center" }}
                    label="Order Status"
                    value={
                      <Autocomplete
                        fullWidth
                        size="small"
                        options={isDistributor ? orderStatusOptions(orderDetails?.status) : orderStatusOptionsClient || []}
                        value={
                          orderStatusOptions(orderDetails?.status)?.find(
                            (option) =>
                              option.value ===
                              getIn(formik.values, "order_status")
                          ) ?? {
                            label: "",
                            value: "",
                          }
                        }
                        onChange={(event, newValue) => {
                          if (newValue.value) {
                            formik.setErrors({});
                            formik.setFieldValue("order_status", newValue.value || "")
                          };
                        }
                        }
                        onBlur={() =>
                          formik.setFieldTouched("order_status", true)
                        }
                        getOptionLabel={(option) => option.label || ""}
                        isOptionEqualToValue={(option, value) =>
                          option.value === value?.value
                        }
                        popupIcon={<GrFormDown size={18} />}
                        disableClearable
                        filterSelectedOptions
                        disabled={editableForm.isChange}
                        className={`${classes.DropDown} autocomplete-wrapper`}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            disabled={editableForm.isChange}
                            error={
                              !!(
                                formik.touched.order_status &&
                                formik.errors.order_status
                              )
                            }
                            helperText={
                              formik.touched.order_status &&
                                formik.errors.order_status
                                ? formik.errors.order_status
                                : ""
                            }
                            size="small"
                            InputLabelProps={{
                              className: params.InputProps.className || "",
                            }}
                            InputProps={{
                              ...params.InputProps,
                              startAdornment: (
                                <>
                                  {getIn(formik.values, "order_status") && (
                                    <TimelineDot
                                      sx={{
                                        backgroundColor:
                                          statusColor[
                                          getIn(formik.values, "order_status")
                                            .toString()
                                            .toLowerCase()
                                          ],
                                        margin: "17px 0 0 14px",
                                        padding: "2px !important",
                                      }}
                                    />
                                  )}
                                  {params.InputProps.startAdornment}
                                </>
                              ),
                            }}
                          />
                        )}
                      />
                    }
                  />
                ) : (
                  <DetailsItem
                    label="Order Status"
                    value={
                      <StatusText
                        status={["PENDING", "ONHOLD"].includes(orderDetails?.status) ? orderStatusDisplay[orderDetails?.status] : capitalizeFirstLetter(orderDetails?.status)}
                        message={["PENDING", "ONHOLD"].includes(orderDetails?.status) ? orderStatusDisplay[orderDetails?.status] : capitalizeFirstLetter(orderDetails?.status)}
                        isBold
                        colorFun={simOrderStatusColorMap}
                      />
                    }
                  />
                )}
                <DetailsItem
                  label="Order Date and Time"
                  value={dayjs(orderDetails?.orderDate).format(
                    "DD-MM-YYYY hh:mm:ss"
                  )}
                />
                <DetailsItem
                  label="Quantity and SIM Type:"
                  value={orderDetails?.orderItem.map(
                    (
                      x: { quantity: number; simType: string },
                      inx: number,
                      arr: string | any[]
                    ) => (
                      <React.Fragment key={inx}>
                        {`${x.quantity} - ${formFactor.find((y) => y.value === x.simType)?.title || x.simType} SIMs`}
                        {inx + 1 !== arr.length && (
                          <>
                            <br />{" "}
                            <Box
                              sx={{ borderBottom: "1px dotted #DEDEE6", my: 2 }}
                            />
                          </>
                        )}
                      </React.Fragment>
                    )
                  )}
                />
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    mt: "30px",
                    mb: 2,
                  }}
                >
                  Customer Details
                </Typography>
                <DetailsItem
                  loader={loader}
                  label="Customer Account ref"
                  value={simOrderDetails?.customerDetails?.customerReference || "N/A"}
                />
                <DetailsItem
                  loader={loader}
                  label="Name of person placing order"
                  value={
                    simOrderDetails?.customerDetails?.personPlacingOrder || "N/A"
                  }
                />
                <DetailsItem
                  loader={loader}
                  label="Email of person placing order"
                  value={simOrderDetails?.customerDetails?.customerEmail || "N/A"}
                />
                <DetailsItem
                  loader={loader}
                  label="Phone number"
                  value={simOrderDetails?.customerDetails?.customerContactNo || "N/A"}
                />
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    mt: "30px",
                    mb: 2,
                  }}
                >
                  Shipping Details
                </Typography>
                <DetailsItem
                  loader={loader}
                  label="Contact Customer Name"
                  value={simOrderDetails?.shippingDetails?.contactName || "N/A"}
                />
                <DetailsItem
                  loader={loader}
                  label="Customer Shipping Address"
                  value={
                    <>
                      {simOrderDetails?.shippingDetails?.addressLine1} <br />
                      {simOrderDetails?.shippingDetails?.addressLine2} <br />
                      {simOrderDetails?.shippingDetails?.city} <br />
                      {simOrderDetails?.shippingDetails?.postalCode} <br />
                      {simOrderDetails?.shippingDetails?.stateOrRegion} <br />
                      {
                        countryOptions?.find(
                          (x) =>
                            x.value === simOrderDetails?.shippingDetails?.country
                        )?.label
                      }{" "}
                      <br />
                    </>
                  }
                />
                {formik.values?.order_status === "SHIPPED" && (
                  <>
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      sx={{
                        mt: "30px",
                        mb: 2,
                      }}
                    >
                      Tracking Details
                    </Typography>
                    {isAuthorize ? (
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          gap: "24px",
                        }}
                      >
                        <TextFieldWrapper
                          className={classes.textField}
                          inputRef={inputRef1}
                          name="tracking_url"
                          id="tracking_url"
                          label="Tracking URL"
                          formikValue={formik}
                          InputProps={{
                            readOnly: editableForm.isChange,
                          }}
                          onClick={() => {
                            if (!editableForm.isChange) return;
                            handleCopyUrl(inputRef1)
                          }}
                          error={!!formik.errors?.tracking_url}
                        />
                        <TextFieldWrapper
                          className={classes.textField}
                          inputRef={inputRef2}
                          name="tracking_reference"
                          id="tracking_reference"
                          label="Tracking Reference"
                          formikValue={formik}
                          InputProps={{
                            readOnly: editableForm.isChange,
                          }}
                          onClick={() => {
                            if (!editableForm.isChange) return;
                            handleCopyUrl(inputRef2)
                          }}
                          error={!!formik.errors?.tracking_reference}
                        />
                      </Box>
                    ) : (
                      <>
                        <DetailsItem
                          loader={loader}
                          label="Tracking URL"
                          value={simOrderDetails?.orderTracking?.tracking_url}
                        />
                        <DetailsItem
                          loader={loader}
                          label="Tracking Reference"
                          value={
                            simOrderDetails?.orderTracking?.tracking_reference
                          }
                        />
                      </>
                    )}
                  </>
                )}
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  sx={{
                    mt: "30px",
                    mb: 2,
                  }}
                >
                  Additional Details
                </Typography>
                <DetailsItem
                  loader={loader}
                  label="Any other information"
                  value={
                    simOrderDetails?.shippingDetails?.otherInformation || "N/A"
                  }
                />
                {["CANCELLED", "ONHOLD"].includes(formik.values?.order_status) && (
                  <>
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      sx={{
                        mt: "30px",
                        mb: 2,
                      }}
                    >
                      Reason
                    </Typography>
                    <Box>
                      <TextFieldWrapper
                        className={classes.textField}
                        name="reject_reason"
                        id="reject_reason"
                        label="Comment"
                        formikValue={formik}
                        disabled={editableForm.isChange}
                        InputProps={{
                          readOnly: initialFormValues?.order_status === "ONHOLD",
                        }}
                        error={!!formik.errors?.reject_reason}
                      />
                    </Box>
                  </>
                )}
              </Box>
              <Box>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "flex-start",
                    p: 5,
                    gap: 4,
                    fontSize: "15px",
                    fontWeight: "700",
                    backgroundColor: currentTheme?.bgLightPrimary ||  "#F5F1FA",
                    ".resetToDefault": {
                      color: "#525252 !important",
                      backgroundColor: "transparent",
                      textTransform: "unset",
                    },
                  }}
                >
                  <LoadingButton
                    variant="contained"
                    color="primary"
                    type="submit"
                    loading={loader}
                    sx={{
                      textTransform: "unset",
                      cursor: "pointer",
                    }}
                    data-testid="save-changes-btn"
                  >
                    {drawerButtonCondition ? "CONFIRM" : "OK"}
                  </LoadingButton>
                  {drawerButtonCondition && (
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => onClose()}
                      className="resetToDefault"
                      data-testid="close-button"
                    >
                      CANCEL
                    </Button>
                  )}
                </Box>
              </Box>
            </form>
          );
        }}
      </Formik>
    </CommonDrawer>
  );
};
export default SimDetailsDrawer;
