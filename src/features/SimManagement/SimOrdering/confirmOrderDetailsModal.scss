.confirm-order-modal {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 16px;
  }

  &__title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
  }

  &__confirmation-text {
    margin-bottom: 24px;
    font-size: 14px;
    color: #555;
  }

  &__order-details {
    margin-bottom: 24px;
  }

  &__table-header {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
  }

  &__table-row {
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
  }

  &__table-header-text {
    font-size: 14px;
    font-weight: 700;
    color: #333;
  }

  &__table-cell-text {
    font-size: 14px;
    color: #555;
  }

  &__loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
  }

  &__buttons {
    display: flex;
    justify-content: flex-start;
    gap: 16px;
    margin-top: 24px;
    padding-top: 16px;
  }

  &__confirm-button {
    min-width: 110px;
    text-transform: uppercase;
    font-size: 12px;
  }

  &__cancel-button {
    min-width: 110px;
    text-transform: uppercase;
    font-size: 12px;
    background-color: var(--action-bg, #ebe3f6);
  }
}
