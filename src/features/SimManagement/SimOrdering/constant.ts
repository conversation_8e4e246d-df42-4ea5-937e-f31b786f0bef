export const sortFieldNames = {
  customerAccountName: 'customer_account_name',
  orderId: 'order_id',
  orderItem: 'order_item',
  orderDate: 'order_date',
  orderBy: 'customer_name',
  customerEmail: 'customer_email',
  customerPhone: 'customer_contact_no',
  orderStatusHistory: 'status',
  actions: 'actions',
};
 
export const descending = 'desc';
 
export const countryOptions = [
  { value: "GB", label: "United Kingdom" },
];
 
export const simTypeObj = {
  sim_type: "",
  quantity: "",
};
 
export const orderStatusOptions = (sts: string) => {
  if (sts === "PENDING") {
    return [
      { label: "Pending Approval", value: "PENDING" },
      { label: "Approve Order", value: "APPROVED" },
      { label: "Hold Order", value: "ONHOLD" },
      { label: "Cancel Order", value: "CANCELLED" },
    ];
  } else if (sts === "APPROVED") {
    return [
      { label: "Approve Order", value: "APPROVED" },
      { label: "Ship Order", value: "SHIPPED" },
      { label: "Cancel Order", value: "CANCELLED" },
    ];
  } else if (sts === "ONHOLD") {
    return [
      { label: "On Hold", value: "ONHOLD" },
      { label: "Approve Order", value: "APPROVED" },
      { label: "Cancel Order", value: "CANCELLED" },
    ];
  } else {
    return [
      { label: "Pending Approval", value: "PENDING" },
      { label: "Approve Order", value: "APPROVED" },
      { label: "Ship Order", value: "SHIPPED" },
      { label: "Cancel Order", value: "CANCELLED" },
    ];
  }
};
 
export const orderStatusOptionsClient = [
  { label: "Pending Approval", value: "PENDING" },
  { label: "Cancel Order", value: "CANCELLED" },
];
 
export const orderStatusDisplay = {
  PENDING: "Pending Approval",
  ONHOLD: "On Hold",
}