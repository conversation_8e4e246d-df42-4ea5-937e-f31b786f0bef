import React from 'react';
import './EnhancedTableSkeleton.scss';

interface EnhancedTableSkeletonProps {
  count: number;
  optionalClass?: string; // optionalClassal prop to add additional class
}

const EnhancedTableSkeleton: React.FC<EnhancedTableSkeletonProps> = ({ count, optionalClass = '' }) => (
  <div
    className={`enhanced-table-listview-skeleton ${optionalClass}`}
    data-testid="enhanced-table-listview-skeleton"
  >
    {[...Array(count)].map((_, i) => (
      <div
        key={i}
        className="enhanced-table-listview-skeleton__item"
        style={{ top: `${i * 70}px` }}
      >
        <div className="enhanced-table-listview-skeleton__item_left" />
      </div>
    ))}
  </div>
);

export default EnhancedTableSkeleton;
