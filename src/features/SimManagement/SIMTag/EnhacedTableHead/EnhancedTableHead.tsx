import React from 'react';
import {
  Box,
  TableCell, TableHead, TableRow, TableSortLabel,
} from '@mui/material';
import { visuallyHidden } from '@mui/utils';

type Order = 'asc' | 'desc';
interface Data {
  accountName: string;
  tagName: string;
  note: string;
  linkedSIMCount: string;
  createdBy: string;
  createdDate: string;
  actions: string;
}

export const sortFieldNames: Data = {
  accountName: 'name',
  tagName: 'tag_name',
  note: 'tag_note',
  linkedSIMCount: 'linked_sim',
  createdBy: 'created_by',
  createdDate: 'created_at',
  actions: 'actions',
};

interface HeadCell {
  id: keyof Data;
  label: string;
  sorting: boolean;
  width: string;
  flex: number;
}

const headCells: readonly HeadCell[] = [
  {
    id: 'accountName',
    label: 'Account',
    sorting: true,
    width: '10%',
    flex: 1,
  },
  {
    id: 'tagName',
    label: 'Tag Name',
    sorting: true,
    width: '5%',
    flex: 1,
  },
  {
    id: 'note',
    label: 'Note',
    sorting: false,
    width: '40%',
    flex: 1,
  },
  {
    id: 'linkedSIMCount',
    label: 'Linked SIM(s)',
    sorting: true,
    width: '5%',
    flex: 1,
  },
  {
    id: 'createdBy',
    label: 'Created By',
    sorting: true,
    width: '10%',
    flex: 1,
  },
  {
    id: 'createdDate',
    label: 'Creation Date',
    sorting: true,
    width: '10%',
    flex: 1,
  },
  {
    id: 'actions',
    label: 'Actions',
    sorting: false,
    width: '20%',
    flex: 1,
  },
];

interface EnhancedTableProps {
  onRequestSort: (event: React.MouseEvent<unknown>, property: keyof Data) => void;
  order: Order;
  orderBy: string;
}

export default function EnhancedTableHead(props: EnhancedTableProps) {
  const {
    order, orderBy, onRequestSort,
  } = props;
  const createSortHandler = (property: keyof Data) => (event: React.MouseEvent<unknown>) => {
    onRequestSort(event, property);
  };
  const cssProperties: any = visuallyHidden;
  return (
    <TableHead>
      <TableRow sx={{ minWidth: '1863px' }}>
        {headCells.map((headCell, index) => (
          <TableCell
            key={headCell.id}
            // padding={headCell.disablePadding ? 'none' : 'normal'}
            sortDirection={orderBy === headCell.id ? order : false}
            variant="head"
            align="left"
            sx={{
              backgroundColor: 'lightblue',
              width: '260px',
              flex: headCell.flex,
              minWidth: headCell.width,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              '& .MuiTableSortLabel-root': {
                marginLeft: (index === 0) ? '12px' : '0px',
              },
            }}
          >
            {headCell.sorting ? (
              <TableSortLabel
                active={orderBy === headCell.id}
                direction={orderBy === headCell.id ? order : 'asc'}
                onClick={createSortHandler(headCell.id)}
              >
                {headCell.label}
                {orderBy === headCell.id ? (
                  <Box component="span" sx={cssProperties}>
                    {order === 'desc' ? 'sorted descending' : 'sorted ascending'}
                  </Box>
                ) : null}
              </TableSortLabel>
            ) : (
              <Box>
                {headCell.label}
              </Box>
            )}

          </TableCell>
        ))}
      </TableRow>
    </TableHead>
  );
}
