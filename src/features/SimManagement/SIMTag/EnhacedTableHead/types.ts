export type Order = 'asc' | 'desc';

export interface HeadCell<T> {
  id: keyof T;
  label: string;
  disablePadding?: boolean;
  numeric?: boolean;
  sorting?: boolean;
  width?: string;
}

export interface EnhancedTableProps<T> {
  columns: HeadCell<T>[];
  data: T[];
  loading: boolean;
  sortConfig: {
    key: keyof T | null;
    direction: Order | null;
  };
  onSort: (key: keyof T) => void;
  renderRow: (row: T, index: number) => React.ReactNode;
  pagination?: {
    total: number;
    page: number;
    pageSize: number;
    onPageChange: (newPage: number) => void;
    onPageSizeChange: (size: number) => void;
  };
  showSkeleton?: boolean;
  noDataPlaceholder?: React.ReactNode;
  className?: string;
}
