import React from 'react';

import SimIcon from 'assets/images/SimIcon';
import {
  Button, Box, Stack, Tooltip,
} from '@mui/material';
import StatusText from 'shared/Status/StatusText';

export const SimTotal = ({ sim, totalSim }) => {

  const statusTypes = [
    { key: 'Standard', label: 'Standard (2FF)', status: 'standard' },
    { key: 'Micro', label: 'Micro (3FF)', status: 'micro' },
    { key: 'Nano', label: 'Nano (4FF)', status: 'nano' },
    { key: 'esim', label: 'eSIM (MFF2)', status: 'esim' },
    { key: 'esim_euicc', label: 'eSIM (MFF2 eUICC)', status: 'esim_euicc' },
  ];

  return (
    <Tooltip
      title={(
        <Stack direction="column" spacing={1}>
          {statusTypes.map(({ key, label, status }) => (
            <StatusText
              key={key}
              status={status}
              message={`${sim?.[key] ?? 0} ${label}`}
            />
          ))}
        </Stack>
      )}
      arrow
      placement="top"
    >
      <Button
        variant="text"
        size="small"
        sx={{
          height: '44px',
          marginLeft: '15px',
          marginRight: '15px',
          padding: '10px 15px',
          background: '#efeaf7',
          svg: {
            width: '30px',
            height: '100%',
          },

        }}
        startIcon={<SimIcon />}
      >

        <Box sx={{
          display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2.5, color: '#696969',
        }}
        >
          {totalSim}
        </Box>

      </Button>
    </Tooltip>

  )
};

SimTotal.defaultProps = {
  activeSim: 0,
  totalSim: 0,
};
export default SimTotal;
