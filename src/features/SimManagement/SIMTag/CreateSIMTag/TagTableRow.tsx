import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ack, TableCell, TableRow, Tooltip } from '@mui/material';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { StyledBoxName } from 'features/SimManagement/componentUIparts';
import React from 'react';
import { AiOutlineDelete } from 'react-icons/ai';
import { GrEdit } from 'react-icons/gr';
import Image from 'shared/LazyLoad/Image';
import Tag from 'shared/Tag/Tag';
import SimTotal from '../SIMTotal';
import dayjs from 'dayjs';

export interface SIMTagTableRow {
  uuid: string;
  accountId: number;
  accountName: string;
  accountLogoKey: string;
  accountLogoUrl: string;
  tagName: string;
  tagNote: string;
  tagColor: string;
  totalSim: number;
  sim: {
    Standard?: number;
    Micro?: number;
    Nano?: number;
    [key: string]: number | undefined;
  };
  createdBy: string;
  createdDate: string;
}

interface Props {
  row: any
  onEdit: (row: any) => void
  onDelete: (row: any) => void
  setTagValidationPopup: React.Dispatch<React.SetStateAction<boolean>>
  setDeletePopup: React.Dispatch<React.SetStateAction<boolean>>
}

const TagTableRow: React.FC<Props> = ({ row, onEdit, onDelete, setTagValidationPopup, setDeletePopup }) => {

  return (
    <TableRow
      data-testid="table-row"
      key={row.id}
      sx={{
        ':hover': {
          cursor: 'pointer',
        },
      }}>
      {/* Account */}
      <TableCell>
        <Stack direction="row" spacing={1} marginLeft={3} alignItems="center" gap={5}>
          <Box
            component="div"
          >
            <Image
              src={row.accountLogoUrl || ''}
              alt={row.accountName}
              style={
                {
                  display: 'block',
                  maxWidth: '30px',
                  width: '57px',
                  height: 'auto',
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }
              }
            />
          </Box>
          <Tooltip title={row?.accountName} arrow placement="top">
            <StyledBoxName>
              {row.accountName}
            </StyledBoxName>
          </Tooltip>
        </Stack>
      </TableCell>

      {/* Tag Name */}
      <TableCell>
        <Tag color={row?.tagColor} text={row?.tagName} />
      </TableCell>

      {/* Note */}
      <TableCell>
        <Tooltip title={row?.tagNote} arrow placement="top">

          {row?.tagNote?.length
            > 35
            ? row.tagNote?.slice(0, 35) + "..."
            : row?.tagNote
          }
        </Tooltip>
      </TableCell>

      {/* Linked SIMs */}
      <TableCell>
        <SimTotal sim={row?.sim || {}} totalSim={row?.totalSim} />
      </TableCell>

      {/* Created By */}
      <TableCell>
        {row?.createdBy}
      </TableCell>

      {/* Creation Date */}
      <TableCell>
        {dayjs(row?.createdAt).format('DD-MM-YYYY hh:mm:ss')}
      </TableCell>

      {/* Actions */}
      <TableCell sx={{
        display: 'flex',
        alignItems: 'center',
      }}>
        <CommonAuthwrapper
          permission={[]}
          repository={[]}
        >
          <Box sx={{
            visibility: 'hidden',
          }}
          >
            <Tooltip title="Edit" arrow placement="top">
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit?.(row?.uuid);
                }}
                data-testid="editButoon"
                size="medium"
                className='black_stroke'
                sx={{ height: '44px', width: '44px', marginLeft: '15px' }}
              >
                <GrEdit size={22} />
              </IconButton>
            </Tooltip>
          </Box>
        </CommonAuthwrapper>

        <CommonAuthwrapper
          permission={[]}
          repository={[]}
        >
          <Box sx={{
            visibility: 'hidden',
          }}
          >
            <Tooltip title="Delete" arrow placement="top">
              <IconButton
                onClick={(e) => {
                  e.stopPropagation();
                  if (row?.totalSim > 0) {
                    setTagValidationPopup(true);
                  } else {
                    setDeletePopup(true);
                  }
                  onDelete?.(row?.id);
                }}
                data-testid="deleteButton"
                size="medium"
                className='black_stroke'
                sx={{ height: '44px', width: '44px', marginLeft: '15px' }}
              >
                <AiOutlineDelete size={22} />
              </IconButton>
            </Tooltip>
          </Box>
        </CommonAuthwrapper>

      </TableCell>
    </TableRow>
  );
};

export default TagTableRow;
