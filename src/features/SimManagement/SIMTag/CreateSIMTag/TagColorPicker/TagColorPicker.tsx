import React from 'react';
import { useField, useFormikContext } from 'formik';
import './TagColorPicker.scss';

const COLORS = [
  '#5B2E91', '#FF00FF', '#333333', '#EC6C6C', '#3CBF92',
  '#0099E5', '#FF7F66', '#00D8FF', '#924DE7', '#C4C4CC',
];

const TagColorPicker = ({ name }: { name: string }) => {
  const { setFieldValue } = useFormikContext();
  const [field] = useField(name);

  return (
    <div className="tag-color-picker">
      {COLORS.map((color) => (
        <div
          key={color}
          className={`color-circle ${field.value === color ? 'selected' : ''}`}
          style={{ backgroundColor: color }}
          onClick={() => setFieldValue(name, color)}
        />
      ))}
    </div>
  );
};

export default TagColorPicker;
