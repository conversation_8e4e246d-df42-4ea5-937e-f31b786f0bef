.custom-form-field {
  margin: 15px;

  .MuiFormControl-root {
    width: 100%;
  }

  .MuiOutlinedInput-root {
    border-radius: 4px;
    background-color: #fff;
    font-size: 13px;
  }

  .MuiSelect-icon {
    right: 14px;
  }

  .MuiOutlinedInput-input {
    height: 35px;
    padding: 0 14px;
    display: flex;
    align-items: center;
    font-size: 13px;
  }

  // Fix for multiline label alignment
  .MuiOutlinedInput-root.MuiInputBase-multiline + fieldset {
    top: -6px;
  }

  textarea {
    resize: none;
  }

  // Textarea input
  .MuiOutlinedInput-root textarea {
    line-height: 1.4;
    min-height: 60px;
    height: auto !important;
    padding: 0;
  }

  .MuiInputLabel-root {
    font-weight: 500;
    font-size: 12px;
    transform: translate(14px, 11px) scale(1);
    transition: all 0.2s ease;
    background: white;
    padding: 0 4px;
  }

  .MuiInputLabel-shrink {
    transform: translate(14px, -9px) scale(0.85);
  }
}

.tag-preview {
  margin: 10px 0;
}
