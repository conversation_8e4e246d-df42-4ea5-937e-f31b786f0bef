import { CloseOutlined } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import CommonDrawer from '@nv2/nv2-pkg-js-shared-components/lib/CommonDrawer';
import { toastError, toastInfo } from 'core/utilities/toastHelper';
import { RESPONSE } from 'features/constants';
import { createSIMTag, getAccountsByName, getSIMtagById, updateSIMTag } from 'features/SimManagement/api.service';
import { IAccount } from 'features/SimManagement/SimManagement.models';
import { Field, Form, Formik } from 'formik';
import React, { useEffect, useState } from 'react';
import { GrDown } from 'react-icons/gr';
import Tag from 'shared/Tag/Tag';
import TitleWrapper from 'shared/TitleWrapper/TitleWrapper';
import * as Yup from 'yup';
import './CreateSIMTag.scss';
import TagColorPicker from './TagColorPicker/TagColorPicker';
import TagDrawerSkeleton from './TagDrawerSkeleton';

interface CreateSIMTagProps {
  onClose: () => void;
  open: boolean;
  rowData: { id: string } | null;
  mode: string | null;
  makeAutoRefresh: () => void
  isDistributorAdmin: boolean
  userProfile: any
}

const CreateSIMTag: React.FC<CreateSIMTagProps> = ({ onClose, open, rowData, mode, makeAutoRefresh, isDistributorAdmin, userProfile }) => {
  const [accounts, setAccounts] = useState<IAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const initialFormValues = {
    accountId: isDistributorAdmin ? '' : String(userProfile?.accountId),
    tagName: '',
    tagNote: '',
    tagColor: '',
  };
  const [initialValues, setInitialValues] = useState(initialFormValues);
  const validationSchema = Yup.object().shape({
    ...(isDistributorAdmin ? {} : {
      accountId: Yup.string().required('Account is required'),
    }),
    tagName: Yup.string().required('Tag Name is required'),
    tagNote: Yup.string().required('Note is required'),
    tagColor: Yup.string().required('Tag Color is required'),
  });


  const getAccountList = async () => {
    try {
      const response = await getAccountsByName();
      if (response.data?.length > 0) {
        setAccounts(response.data);
      }
    } catch {
      toastInfo(RESPONSE.GET_SIMACTION_EMPTY);
    }
  };

  const fetchTagData = async () => {
    if (mode === 'edit' && rowData?.id) {
      try {
        setLoading(true);
        const response = await getSIMtagById(rowData.id);
        const data = response?.data;
        setInitialValues({
          accountId: data?.accountId || '',
          tagName: data?.tagName || '',
          tagNote: data?.tagNote || '',
          tagColor: data?.tagColor || '',
        });
      } catch (err) {
        toastError('Failed to load tag for editing.');
        onClose();
      } finally {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    if (isDistributorAdmin) {
      getAccountList();
    }
  }, []);

  const handleClose = (resetForm: any) => {
    onClose();
    resetForm();
    setInitialValues(initialFormValues);
  };

  const handleCreateTag = async (values) => {
    try {
      setLoading(true);
      const response = await createSIMTag(values);
      toastInfo('Tag created successfully.');
      makeAutoRefresh();
    } catch (e) {
      console.error('Create Tag Error:', e);
      toastError('Failed to create tag.');
    } finally {
      setLoading(false);
      onClose();
    }
  };

  const handleEditTag = async (values) => {
    if (!rowData?.id) return toastError('Tag ID missing for edit.');
    try {
      setLoading(true);
      const response = await updateSIMTag(rowData.id, values);
      toastInfo('Tag updated successfully.');
      makeAutoRefresh();
    } catch (e) {
      console.error('Edit Tag Error:', e);
      toastError('Failed to update tag.');
    } finally {
      setLoading(false);
      onClose();
    }
  };

  useEffect(() => {
    if (mode === 'edit' && open) {
      fetchTagData();
    }
  }, [mode, rowData?.id, open]);

  return (
    <Formik
      initialValues={initialValues}
      enableReinitialize={true}
      validationSchema={validationSchema}
      onSubmit={async (values, { resetForm }) => {
        if (mode === 'edit') {
          await handleEditTag(values);
        } else {
          await handleCreateTag(values);
        }
        resetForm();
      }}
    >
      {({ values, setFieldValue, resetForm, errors, touched, isValid, dirty, handleSubmit }) => {
        return (
          <CommonDrawer
            anchor="right"
            open={open}
            onClose={() => handleClose(resetForm)}
            heading="Create Tag"
            variant="temporary"
          >
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              className="drawer-header"
              px={3}
            >
              <h3 className="drawer-header-title">
                {mode === 'edit' ? 'Edit Tag' : 'Create Tag'}
              </h3>
              <CloseOutlined fontSize="large" onClick={() => handleClose(resetForm)} />
            </Box>
            {(mode === 'edit' && loading) ? (
              <TagDrawerSkeleton />
            ) : (
              <Form data-testid="rule-form">
                <Box
                  sx={{
                    height: 'calc(100vh - 135px)',
                    maxWidth: '540px',
                    overflowY: 'auto',
                    p: 3,
                    fontFamily: 'Open Sans, sans-serif',
                  }}
                  className="scrollBar"
                >
                  {isDistributorAdmin &&
                    <>
                      {/* Account */}
                      <TitleWrapper title="Account" />
                      <Box className="custom-form-field">
                        <Field name="accountId">
                          {({ field }: any) => (
                            <FormControl fullWidth>
                              <InputLabel id="account-select-label">Select Account</InputLabel>
                              <Select
                                {...field}
                                labelId="account-select-label"
                                label="Select Account"
                                onChange={(e) => setFieldValue('accountId', e.target.value)}
                                IconComponent={GrDown}
                              >
                                {accounts.map((account) => (
                                  <MenuItem key={account.id} value={account.id}>
                                    {account.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        </Field>
                      </Box>
                    </>}

                  {/* Tag Details */}
                  <TitleWrapper title="Tag Details" />

                  <Box className="custom-form-field">
                    <Field name="tagName">
                      {({ field }: any) => (
                        <TextField
                          {...field}
                          label="Tag Name"
                          placeholder="Enter tag name"
                          fullWidth
                        />
                      )}
                    </Field>
                  </Box>

                  <Box className="custom-form-field">
                    <Field name="tagNote">
                      {({ field }: any) => (
                        <TextField
                          {...field}
                          label="Note"
                          placeholder="Enter optional note"
                          fullWidth
                          multiline
                          minRows={3}
                        />
                      )}
                    </Field>
                  </Box>

                  {/* Tag Color */}
                  <TitleWrapper title="Tag Color" />
                  <TagColorPicker name="tagColor" />

                  {/* Tag Preview */}
                  <TitleWrapper title="Tag Preview" />
                  {
                    values?.tagName &&
                    <Box className="tag-preview">
                      <Tag color={values.tagColor} text={values.tagName} />
                    </Box>
                  }
                </Box>
              </Form>
            )
            }
            {/* Footer Buttons */}
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  justifyContent: 'flex-start',
                  gap: 2,
                  p: 3,
                  backgroundColor: '#F5F1FA',
                }}
              >
                <LoadingButton
                  variant="contained"
                  color="primary"
                  type="submit"
                  onClick={() => handleSubmit()}
                  loading={loading}
                  disabled={!isValid || !dirty}
                  sx={{ textTransform: 'unset' }}
                >
                  CONFIRM
                </LoadingButton>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => handleClose(resetForm)}
                  sx={{
                    textTransform: 'unset',
                    backgroundColor: 'transparent',
                    color: '#525252',
                  }}
                >
                  CANCEL
                </Button>
              </Box>
            </Box>
          </CommonDrawer>
        )
      }}
    </Formik>
  );
};

export default CreateSIMTag;
