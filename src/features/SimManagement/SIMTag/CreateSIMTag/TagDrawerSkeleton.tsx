import React from 'react';
import { Box, Skeleton, Stack } from '@mui/material';
import './TagDrawerSkeleton.scss';

const TagDrawerSkeleton = () => {
  const colorCount = 10;

  return (
    <Box className="tag-form-skeleton" p={5}>

      {/* Section: Account */}
      {/* <Skeleton variant="rounded" height={25} width="20%" className="mb-1" /> */}
      <Skeleton variant="rounded" height={40} width="100%" className="mb-4" />

      {/* Section: Tag Details */}
      {/* <Skeleton variant="rounded" height={25} width="20%" className="mb-1" /> */}
      <Skeleton variant="rounded" height={40} width="100%" className="mb-2" />
      <Skeleton variant="rounded" height={80} width="100%" className="mb-4" />

      {/* Section: Tag Color */}
      {/* <Skeleton variant="rounded" height={25} width="20%" className="mb-1" /> */}
      <Stack direction="row" spacing={2} className="mb-4">
        {Array.from({ length: colorCount }).map((_, index) => (
          <Skeleton key={index} variant="circular" width={28} height={28} />
        ))}
      </Stack>

      {/* Section: Tag Preview */}
      {/* <Skeleton variant="rounded" height={25} width="20%" className="mb-1" /> */}
      <Skeleton variant="rectangular" height={40} width="100%" />
    </Box>
  );
};

export default TagDrawerSkeleton;
