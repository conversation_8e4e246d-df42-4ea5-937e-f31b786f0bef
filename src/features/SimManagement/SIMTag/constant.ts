import { HeadCell } from "./EnhacedTableHead/types";

export const sortFieldNames = {
  accountName: 'account_name',
  requestId: 'request_id',
  action: 'action',
  field: 'field',
  user: 'user',
  clientIp: 'client_ip',
  createdDate: 'created_date',
  status: 'status',
  actions: 'actions',
};

export const descending = 'desc';

export interface SIMTagTableRow {
  uuid: string;
  accountId: number;
  accountName: string;
  accountLogoKey: string;
  accountLogoUrl: string;
  tagName: string;
  tagNote: string;
  tagColor: string;
  totalSim: number;
  sim: {
    Standard?: number;
    Micro?: number;
    Nano?: number;
    [key: string]: number | undefined;
  };
  createdBy: string;
  createdDate: string;
}


export const simTagSampleData: SIMTagTableRow[] = [
  {
    "uuid": "9fb65612-45ce-4e79-a519-b67fc302b3f5",
    "accountId": 1,
    "accountName": "Account A",
    "accountLogoKey": "/home/<USER>/tmp/monoglass/images/bt-logo.svg",
    "accountLogoUrl": "http://localhost:8000//home/<USER>/tmp/monoglass/images/bt-logo.svg",
    "tagName": "HIGH-USAGE",
    "tagNote": "Simple Tag Creation",
    "tagColor": "#f70303ff",
    "totalSim": 300,
    "sim": {
      "Standard": 50,
      "Micro": 100,
      "Nano": 150
    },
    "createdBy": "<EMAIL>",
    "createdDate": "2025-07-01 10:05:34.433000"
  },
  {
    "uuid": "9fb65612-45ce-4e79-a519-b67fc302b3f6",
    "accountId": 2,
    "accountName": "Account B",
    "accountLogoKey": "/home/<USER>/tmp/monoglass/images/bt-logo1.svg",
    "accountLogoUrl": "http://localhost:8000//home/<USER>/tmp/monoglass/images/bt-logo1.svg",
    "tagName": "IOT-DEVICES",
    "tagNote": "Simple 2 Tag Creation",
    "tagColor": "#1a128fff",
    "totalSim": 200,
    "sim": {
      "Standard": 100,
      "Micro": 50,
      "Nano": 50
    },
    "createdBy": "<EMAIL>",
    "createdDate": "2025-07-01 10:05:34.433000"
  }
];



export const simTagColumns: HeadCell<any>[] = [
  {
    id: 'accountName',
    label: 'Account',
    sorting: true,
    width: '200px',
  },
  {
    id: 'tagName',
    label: 'Tag Name',
    sorting: true,
    width: '180px',
  },
  {
    id: 'note',
    label: 'Note',
    sorting: false,
    width: '300px',
  },
  {
    id: 'linkedSIMCount',
    label: 'Linked SIM(s)',
    sorting: true,
    width: '160px',
  },
  {
    id: 'createdBy',
    label: 'Created By',
    sorting: true,
    width: '180px',
  },
  {
    id: 'createdAt',
    label: 'Creation Date',
    sorting: true,
    width: '180px',
  },
  {
    id: 'action',
    label: 'Actions',
    sorting: false,
    width: '180px',
  },
];
