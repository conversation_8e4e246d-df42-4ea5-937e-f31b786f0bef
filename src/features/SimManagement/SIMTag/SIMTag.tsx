import { Box, Button, MenuItem, Pagination, Select, Stack, Table, TableBody, TableRow, Tooltip, Typography } from '@mui/material';
import axios, { AxiosResponse } from 'axios';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import useAbortController from 'core/hooks/useAbortController';
import { toastError, toastInfo } from 'core/utilities/toastHelper';
import { SIMTagsMessage, SIMTagsValidationMessage, TOASTS } from 'features/constants';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import React, { useEffect, useState } from 'react';
import { AiOutlineTag } from 'react-icons/ai';
import { GrUnorderedList } from 'react-icons/gr';
import { useLocation, useNavigate } from 'react-router-dom';
import GenericDialog from 'shared/Dialog/GenericDialog';
import SearchInput from 'shared/SearchInput';
import TopBar from 'shared/TopBar';
import { organizationTypes } from 'user.model';
import { deleteTag, getAllSIMTags } from '../api.service';
import { StyledEnhacedTableContainer } from '../componentUIparts';
import { SimOrderingInterface } from '../SimManagement.models';
import SimManagementPlaceholder from '../SimManagementPlaceholder';
import { sortFieldNames } from './constant';
import CreateSIMTag from './CreateSIMTag/CreateSIMTag';
import TagTableRow from './CreateSIMTag/TagTableRow';
import EnhancedTableHead from './EnhacedTableHead/EnhancedTableHead';
import EnhancedTableSkeleton from './EnhacedTableHead/EnhancedTableSkeleton';

enum Order {
  asc = 'asc',
  desc = 'desc',
}

interface SortConfig {
  key: string | number | symbol | null;
  direction: Order | null;
}

const SIMTag = ({ user }: SimOrderingInterface) => {
  const [sortConfig, setSortConfig] = useState<any>({ key: null, direction: null });
  const [searchValue, setSearchValue] = useState<string>('');
  const [error, setAccountsError] = useState(false);
  const [data, setData] = useState<any>([])
  const [loading, setLoading] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [tagValidationPopup, setTagValidationPopup] = useState(false);
  const [deletePopup, setDeletePopup] = useState(false);
  const { cancelPreviousRequest, setNewController } = useAbortController();
  const navigate = useNavigate();
  const baseUrl = window.location.origin;

  const location = useLocation();
  const [confirmDialog, setConfirmDialog] = useState<{
    isEnable: boolean;
    rowData: any | null;
    mode: string | null;
  }>({
    isEnable: false,
    rowData: null,
    mode: null,
  });
  const userProfile = JSON.parse(localStorage.getItem('userDetails') || 'null');
  const isDistributorAdmin = user?.organization?.type === organizationTypes.DISTRIBUTOR;

  const handleEdit = (id: string) => {
    setConfirmDialog({
      isEnable: true,
      rowData: { id },
      mode: 'edit',
    });
  };

  const handleDelete = (id: string) => {
    setConfirmDialog({
      isEnable: false,
      rowData: { id },
      mode: 'delete',
    });
  };

  const deleteTagById = async (id: string) => {
    if (!id) return toastError('Invalid tag ID.');
    try {
      setConfirmLoading(true);
      await deleteTag(id);
      toastInfo('Tag deleted successfully.');
      makeAutoRefresh();
    } catch (error) {
      console.error('Delete Tag Error:', error);
      toastError('Failed to delete tag.');
    } finally {
      setDeletePopup(false);
      setConfirmLoading(false);
    }
  };

  const handleClose = () => {
    setConfirmDialog({ isEnable: false, rowData: null, mode: null });
  }

  const handleCreate = () => {
    setConfirmDialog({ isEnable: true, rowData: null, mode: 'create' });
  }

  const CreateTagButton = () => (
    <CommonAuthwrapper permission={[]} repository={[]}>
      <Tooltip title={`Create TAG(s)`} arrow placement='top'>
        <Button
          disabled={false}
          onClick={(e) => {
            e.preventDefault();
            // navigate(setAppRoute('/sim-order-form'));
            handleCreate();
          }}
          component="button"
          sx={{
            p: "0px 25px",
            minWidth: "110px",
            textTransform: "uppercase",
            fontSize: "12px !important",
          }}
          variant="contained"
          color="primary"
          startIcon={<AiOutlineTag />}
          onMouseDown={(e) => e.preventDefault()}
          data-testid="create-allocation-modal__body_buttons_confirm"
        >
          {`Create Tag`}
        </Button>
      </Tooltip>
    </CommonAuthwrapper>
  );

  const {
    generateParamsForUrl,
    getParamsFromUrl,
  } = useMuiTableSearchParams();

  const {
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const noDataTags = {
    icon: <GrUnorderedList size={28} />,
    title: "No available SIM Tags",
    description: (
      "You can create a new SIM by click on below button."
    ),
    additionalItem: (
      <Box sx={{ py: "16px" }}>
        <CreateTagButton />
      </Box>
    ),
  };

  const getTagList = async (paramPage, paramPageSize, paramSearch, paramField?, paramSort?) => {
    const newSearchParams = generateParamsForUrl(paramPage, paramPageSize, paramField, paramSort, paramSearch);
    const newUrl = `${location.pathname}?tab=sim-tag&${newSearchParams.toString()}`;
    cancelPreviousRequest();
    const { signal } = setNewController();
    navigate(newUrl, { replace: true });
    try {
      setLoading(true);
      let ordering;
      if (paramField) { ordering = sortFieldNames[paramField]; }
      const accountsResponse: AxiosResponse = await getAllSIMTags(
        paramPage,
        paramPageSize,
        ordering,
        paramSort,
        paramSearch,
        !isDistributorAdmin ? userProfile?.accountId : null,
        signal
      );
      let { data } = accountsResponse;
      setData(data);
      setLoading(false);
      setAccountsError(false);
    } catch (err: any) {
      if (axios.isCancel(err)) {
        console.log('Request was canceled:', err.message);
        return;
      }
      setLoading(false);
      setAccountsError(true);
      setData(undefined);
      toastInfo(TOASTS.FAILURE_GET_TAG_LIST);
    }
  };


  const handleChange = (event) => {
    const { value } = event.target;
    let paramPage = parseInt(page.toString(), 10);
    const paramPageSize = parseInt(pageSize.toString(), 10);
    const totalRecords = data?.totalCount ? data?.totalCount : 1;
    if (paramPage === data?.lastPage && paramPageSize < value) {
      paramPage = Math.ceil(totalRecords / value);
    }
    getTagList(paramPage, value, search, field, sort);
  };
  const handleChangePage = (event, pageValue) => {
    getTagList(pageValue, pageSize, search, field, sort);
  };

  const handleSearch = (searchTerm: string) => {
    setSearchValue(searchTerm);
    getTagList(1, pageSize, searchTerm, field, sort);
  };


  const makeAutoRefresh = () => {
    getTagList(1, 10, search);
  }

  // const baseUrl = window.location.origin;
  const pageSizeInt = parseInt(pageSize.toString(), 10);
  const pageInt = parseInt(page.toString(), 10);
  const displayCount = (pageInt * pageSizeInt - 1);
  const pageIntStart = pageInt === 1 ? 0 : ((displayCount - pageSizeInt) + 1);

  useEffect(() => {
    makeAutoRefresh()
  }, []);

  return (
    <Box>
      <TopBar navigateTo={baseUrl} className="sim-tags__top-bar">
        <Stack
          direction="row"
          alignItems="center"
          justifyContent="space-between"
          width="100%"
          spacing={2}
        >
          <SearchInput
            placeholder="Search"
            value={searchValue}
            onChange={(e: any) => handleSearch(e.target.value)}
          />
          {data?.totalCount > 0 && (
            <CreateTagButton />
          )}
        </Stack>
      </TopBar>
      <StyledEnhacedTableContainer>
        <Table
          aria-label="simple table"
          stickyHeader
          className="scrollBar"
        >
          <EnhancedTableHead
            order={sortConfig.direction}
            orderBy={sortConfig.key}
            onRequestSort={(eventData, key) => {
              if (sortConfig.key === key) {
                if (sortConfig.direction === 'asc') {
                  getTagList(page, pageSize, search, key, 'desc');
                  setSortConfig({ key, direction: 'desc' });
                } else if (sortConfig.direction === 'desc') {
                  getTagList(page, pageSize, search);
                  setSortConfig({ key: null, direction: null });
                }
              } else {
                setSortConfig({ key, direction: 'asc' });
                getTagList(page, pageSize, search, key, 'asc');
              }
            }}
          />
          {loading ? (
            <TableBody sx={{ height: '65vh' }}>
              <TableRow
                className="loader-row"
                sx={{
                  position: 'relative',
                  '.rule-form-listView-skeleton': {
                    top: 0,
                  },
                }}
              >
                <EnhancedTableSkeleton count={8} />
              </TableRow>
            </TableBody>
          ) : (
            <TableBody>
              {Array.isArray(data?.results) && data?.results?.map(
                (row: any) => (
                  <TagTableRow
                    row={row}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    setTagValidationPopup={setTagValidationPopup}
                    setDeletePopup={setDeletePopup}
                  />
                )
              )}
            </TableBody>
          )}
        </Table>
        {(!loading && (error || data?.results?.length === 0)) && (
          <Box display="flex" alignItems="center" justifyContent="center" height="50vh">
            <SimManagementPlaceholder
              title="No available SIM Tags"
              text="You can create a new SIM by click on below button."
              icon={<GrUnorderedList />}
              loading={loading}
              children={<CreateTagButton />}
            />
          </Box>
        )}

      </StyledEnhacedTableContainer>
      {(!loading && !error && data?.results && data?.results?.length > 0) && (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          sx={{
            backgroundColor: '#f5f1fa',
            padding: '7px 10px',
            borderRadius: '4px',
            marginTop: '4px',
            '& .MuiButtonBase-root, &.MuiPaginationItem-root': {
              borderRadius: '4px',
              minWidth: '40px',
              minHeight: '40px',
              padding: '4px 10px',
              fontWeight: 700,
              '&.Mui-selected': {
                color: '#5514b4',
                '&:hover': {
                  backgroundColor: '#ccb9e9',
                },
                backgroundColor: '#ccb9e9',
              },
              '&:hover': {
                backgroundColor: '#ebe3f6',
              },
            },
          }}
        >
          <Box display="flex" alignItems="center" gap={3.5}>
            <Typography>Rows per page:</Typography>
            <Select
              labelId="demo-simple-select-label"
              id="demo-simple-select"
              value={pageSize}
              onChange={handleChange}
            >
              <MenuItem value={10}>10</MenuItem>
              <MenuItem value={20}>20</MenuItem>
              <MenuItem value={30}>30</MenuItem>
              <MenuItem value={40}>40</MenuItem>
              <MenuItem value={50}>50</MenuItem>
            </Select>
          </Box>
          <Typography>
            {`${pageIntStart} - ${displayCount} of ${data?.totalCount}`}
          </Typography>
          <Pagination
            count={data?.lastPage || 0}
            defaultPage={page ? parseInt(page.toString(), 10) : 1}
            siblingCount={0}
            boundaryCount={2}
            onChange={handleChangePage}
          />
        </Box>
      )}
      <CreateSIMTag
        isDistributorAdmin={isDistributorAdmin}
        userProfile={userProfile}
        onClose={handleClose}
        open={confirmDialog?.isEnable}
        rowData={confirmDialog?.rowData}
        mode={confirmDialog.mode}
        makeAutoRefresh={makeAutoRefresh}
      />
      <GenericDialog
        open={tagValidationPopup}
        title={"Tag Can’t Be Deleted Yet"}
        firstButtonText="Got it"
        sx={{
          '.MuiTypography-root': {
            maxWidth: '330px'
          }
        }}
        onClose={() => {
          setTagValidationPopup(false);
        }}
        onSuccess={() => {
          setTagValidationPopup(false);
        }}
      >
        {SIMTagsValidationMessage}
      </GenericDialog>
      <GenericDialog
        loading={confirmLoading}
        open={deletePopup}
        title={"Are you sure?"}
        onClose={() => {
          setDeletePopup(false);
        }}
        onSuccess={() => {
          deleteTagById(confirmDialog?.rowData?.id);
        }}
      >
        {SIMTagsMessage}
      </GenericDialog>
    </Box>
  );
};

export default SIMTag;
