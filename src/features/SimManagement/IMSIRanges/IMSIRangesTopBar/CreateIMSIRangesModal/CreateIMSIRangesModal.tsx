import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  styled,
} from '@mui/material';
import { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import React, { FC, useMemo, useState } from 'react';

import { MB_CSV_FILE_SIZE } from 'core/utilities/constants';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import { StyledCreateAllocationModal } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts';
import {
  ICreateIMSIRanges,
  IFreeMsisdn,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';
import {
  createImsiRanges,
} from 'features/SimManagement/api.service';
import {
  CSV_SUPPORTED_FORMATS,
  getTooltipMessages
} from 'features/constants';
import { FormikProps, useFormik } from 'formik';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import { AiOutlineCloudUpload } from 'react-icons/ai';
import { GrCircleInformation, GrClose, GrFormDown } from 'react-icons/gr';
import LatestDropZone from 'shared/Dropzone/LatestDropZone';
import Loader from 'shared/Loader';
import * as Yup from 'yup';
import { StyledCloseButton } from '../IMSIRangeSimModal/IMSIRangeSimModalTable/IMSIRangeSimModalTableUI';
import { formFactor } from '../IMSIRangeSimModal/constants';
import './CreateIMSIRangesModal.scss';
import { initialValues } from './intialData';
import { useAppContext } from 'AppContextProvider';

interface ICreateAllocationModalProps {
  handleClose: (event?: any, reason?: any) => void;
  setImage: (event?: any, reason?: any) => void;
  image: File | undefined;
  fetchData: () => void;
  totalRemainingCount: IFreeMsisdn;
  fetchTotalRemainingCount: () => void;
  setUpdateSIMS: () => void;
}
export const emptyIMSIRange = {
  title: 'No available ranges',
  id: 1,
  disabled: true,
};
const validationSchema = Yup.object().shape({
  title: Yup.string().required('Title is Required.'),
  formFactor: Yup.string().required('SIM Type is Required.'),
  file: Yup.mixed()
    .required('A file is required of valid columns and formats.')
    .test(
      'fileSize',
      'File too large',
      (value) => value && value.size <= MB_CSV_FILE_SIZE,
    )
    .test(
      'fileFormat',
      'Unsupported Format',
      (value) => value && CSV_SUPPORTED_FORMATS.includes(value.type),
    ),
});

const CreateIMSIRangesModal: FC<ICreateAllocationModalProps> = ({
  handleClose,
  setImage,
  image,
  fetchData,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  totalRemainingCount,
  fetchTotalRemainingCount,
  setUpdateSIMS
}) => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { getParamsFromUrl } = useMuiTableSearchParams();
  const { currentTheme } = useAppContext();
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const formik: FormikProps<ICreateIMSIRanges> = useFormik({
    validateOnBlur: false,
    validateOnChange: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();
      if (values.file && values.file !== null) {
        formData.append('file', values.file);
      }
      const paramData = {
        params: {
          title: values.title,
          formFactor: values.formFactor,
        },
      };

      try {
        setLoading(true);
        setErrorMessage(null);
        const res = await createImsiRanges(formData, paramData);
        toastSuccess(res?.data?.message);
        setLoading(false);
        handleClose();
        fetchData();
        fetchTotalRemainingCount();
        setUpdateSIMS();
      } catch (err: any) {
        setErrorMessage(err?.response?.data?.detail);
        formik.setFieldValue('file', null);
        setImage(undefined);
        setLoading(false);
        toastError(err?.response?.data?.detail);
      }
    },
  });

  const uploadImage = (fileData) => {
    formik.setFieldValue('file', fileData);
    setErrorMessage(null);
  };
  const removeImage = () => {
    formik.setFieldValue('file', null);
    setImage(undefined);
  };

  const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className ?? '' }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: 500,
    },
  });

  const hasEID = useMemo(() =>
    formFactor.some(option =>
      option.value === 'eSIM_MFF2_eUICC' && option.value === formik.values.formFactor
    ), [formFactor, formik.values.formFactor]);
  const tooltipMessages = useMemo(() => getTooltipMessages(hasEID), [hasEID]);


  return (
    <StyledCreateAllocationModal
      data-testid="create-imsi-range-modal"
      className="create-imsi-range-modal"
    >
      <div className="create-imsi-range-modal__header">
        <Typography variant="h2" component="h2">
          Create IMSI Range
        </Typography>
        <StyledCloseButton
          data-testid="create-imsi-range-modal_close-button"
          type="button"
          onClick={handleClose}
        >
          <GrClose size={21} />
        </StyledCloseButton>
      </div>
      <div className="create-imsi-range-modal__body">
        <form onSubmit={formik.handleSubmit}>
          <TextField
            autoComplete="off"
            className="create-imsi-range-modal__body_form-control"
            label="Title"
            variant="outlined"
            name="title"
            onChange={formik.handleChange}
            value={formik.values.title}
            error={
              !!formik.errors.title
              && !!formik.touched.title
            }
            inputProps={{ maxLength: 60 }}
          />
          <FormControl className="create-imsi-range-modal__body_form-control">
            <InputLabel>SIM Type</InputLabel>
            <Select
              IconComponent={() => (
                <GrFormDown className="select-arrow-icon" size={21} />
              )}
              label="SIM Type"
              name="formFactor"
              onChange={formik.handleChange}
              value={formik.values.formFactor}
              error={!!formik.errors.formFactor && !!formik.touched.formFactor}
            >
              {formFactor?.map((factor) => (
                <MenuItem key={factor?.value} value={factor?.value}>
                  {factor?.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* <FormControl className="create-imsi-range-modal__body_form-control">
            <InputLabel>MSISDN Type</InputLabel>
            <Select
              IconComponent={() => (
                <GrFormDown className="select-arrow-icon" size={21} />
              )}
              label="MSISDN Type"
              name="msisdnFactor"
              onChange={formik.handleChange}
              value={formik.values.msisdnFactor}
              error={!!formik.errors.msisdnFactor && !!formik.touched.msisdnFactor}
            >
              {MSISDN_TYPE_OPTIONS?.map((msisdn) => (
                <MenuItem key={msisdn.value} value={msisdn.value}>
                  {msisdn?.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl> */}
          <Box display="flex" alignItems="center" marginBottom="10px">
            <Typography
              component="div"
              variant="body2"
              fontWeight={700}
              color="#333333"
            >
              Upload IMSI(s)
            </Typography>
            <CustomWidthTooltip
              arrow
              placement="top"
              title={(
                <>
                  <Typography component="p" fontSize={14}>
                    Following criteria for successful processing:
                  </Typography>
                  <Typography>
                    <ul style={{ textIndent: '5px', fontSize: '13px' }}>
                      {tooltipMessages?.map((item) => (
                        <li key={item}>
                          •
                          &nbsp;
                          {item}
                        </li>
                      ))}
                    </ul>
                  </Typography>
                </>
              )}
            >
              <IconButton
                sx={{
                  height: '0px',
                  '&:hover': {
                    backgroundColor: 'none',
                  },
                }}
              >
                <GrCircleInformation />
              </IconButton>
            </CustomWidthTooltip>
          </Box>

          <LatestDropZone
            name="file"
            primaryColor="primary"
            formik={formik}
            uploadImage={uploadImage}
            setImage={setImage}
            image={image}
            dropzoneImg={<AiOutlineCloudUpload size={35} />}
            dropzoneText="Select CSV file with IMSI Range"
            removeAppImage={removeImage}
            description="File must be in .csv format"
            customErrorMessage={errorMessage}
          />
        </form>
        <div className="create-imsi-range-modal__body_buttons">
          <Button
            disabled={!(formik.isValid && formik.dirty)}
            onClick={(e) => {
              e.preventDefault();
              formik.handleSubmit();
            }}
            component="button"
            sx={{
              p: '0px 25px', minWidth: '110px', textTransform: 'uppercase', fontSize: '12px !important',
            }}
            variant="contained"
            color="primary"
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-imsi-range-modal__body_buttons_confirm"
          >
            Confirm
          </Button>
          <Button
            // className="create-imsi-range-modal__body_cancel-button"
            sx={{
              p: '0px 25px', backgroundColor: currentTheme?.actionBg, border: '0px', textTransform: 'uppercase', fontSize: '12px !important',
            }}
            variant="outlined"
            onClick={handleClose}
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-imsi-range-modal__body_buttons_cancel"
          >
            Cancel
          </Button>
        </div>
      </div>
      {loading && (
        <div
          className="create-imsi-range-modal__loader"
          data-testid="create-imsi-range-modal__loader"
        >
          <Loader staticColor={currentTheme?.bgLightPrimary || "#f5f1fa"} size={60} />
        </div>
      )}
    </StyledCreateAllocationModal>
  );
};

export default CreateIMSIRangesModal;
