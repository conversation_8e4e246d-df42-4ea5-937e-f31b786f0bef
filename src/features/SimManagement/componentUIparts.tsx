import {
  TableCell, styled, TableRow, TableContainer, useMediaQuery,
  Box,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import { IStyledTableCellProps } from './SimManagement.models';

const StyledTableContainer = styled(TableContainer)({
  paddingBottom: 30,
});

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '.MuiTableCell-root': {
    background: theme.palette.background.default,
  },
  '&:nth-child(even) .MuiTableCell-root': {
    background: styles.lightColor100,
  },
  '&:hover .MuiTableCell-root': {
    background: getCurrentThemeColors(theme.palette.primary.main)[100],
  },
}));

const StyledTableCell = styled(TableCell)<IStyledTableCellProps>(({
  bold = false, align = 'left', sticky = false, th = false,
}) => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return ({
    fontSize: isSmallScreen ? 13 : 14,
    fontWeight: bold ? 700 : 400,
    textAlign: align,
    color: styles.darkColor500,
    padding: '7px 16px',
    height: 54,
    position: sticky ? 'sticky' : 'static',
    zIndex: 1,
    left: 0,
    overflow: 'hidden',
    borderLeft: 1,
    border: th ? `1px solid ${styles.lightColor200}` : 'none',
    borderWidth: th ? '1px 0' : 0,
    whiteSpace: 'nowrap',
    filter: sticky ? 'drop-shadow(8px 0px 8px rgba(140, 140, 140, 0.1))' : 'none',
  });
});

  const StyledPaginationContainer = styled(Box)<{ currentTheme: any }>(({ currentTheme  }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '8px',
    backgroundColor: currentTheme?.bgLightPrimary || '#f5f1fa',
    padding: '7px 10px',
    borderRadius: '4px',
    '& .MuiButtonBase-root, & .MuiPaginationItem-root': {
      borderRadius: '4px',
      minWidth: '40px',
      minHeight: '40px',
      padding: '4px 10px',
      fontWeight: 700,
      '&.Mui-selected': {
        color: currentTheme?.primaryColor || '#5514b4',
        backgroundColor: currentTheme?.bgLightMediumPrimay|| '#ccb9e9',
        '&:hover': {
          backgroundColor: currentTheme?.bgLightMediumPrimay || '#ccb9e9',
        },
      },
      '&:hover': {
        backgroundColor: currentTheme?.actionBg || '#ebe3f6',
      },
    },
  }));

const StyledEnhacedTableContainer = styled(TableContainer)(({ theme }) => ({
  minHeight: 'calc(100vh - 210px)',
  overflowX: 'auto',
  '& .MuiTable-root': {
    borderCollapse: 'separate',
    borderSpacing: '0 15px',
  },
  '& .MuiTableCell-body': {
    borderTop: '1px solid #E7E7EE',
    borderBottom: '1px solid #E7E7EE',
    padding: '18px 6px',
    fontFamily: 'BTCurve, sans-serif',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '14px',
    lineHeight: '140%',
    color: '#333333',
    '&:first-of-type, &:last-of-type': {
      borderRadius: '4px',
    },

  },
  '.action-table-cell': {
    '& .MuiButtonBase-root': {
      '&:hover path': {
        fill: theme.palette.secondary.main,
      },
    },
  },
  '& .MuiTableBody-root': {
    '& .MuiTableCell-body': {
      ':first-of-type': {
        borderLeftWidth: '4px',
        borderLeftStyle: 'solid',
        borderLeftColor: theme.palette.grey,
      },
    },
  },
  '& .MuiTableBody-root .MuiTableRow-root:hover': {
    backgroundColor: 'unset',
    boxShadow: '0px 6px 16px rgba(136, 91, 203, 0.15)',
    '& .MuiTableCell-body': {
      borderTop: 'none',
      ':first-of-type': {
        borderLeftWidth: '4px',
        borderLeftStyle: 'solid',
        borderLeftColor: theme.palette.secondary.main,
      },
    },

    '& .MuiBox-root': {
      visibility: 'visible',
    },

  },
  '& .MuiTableCell-head': {
    minWidth: '110px',
    paddingLeft: '0px',
    borderTopColor: 'unset',
    fontFamily: 'BT Curve, sans-serif',
    border: 'unset',
    backgroundColor: '#fff !important',
    fontStyle: 'normal',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: '17px',
    color: '#525252',
    alignItems: 'center',
    '&:first-of-type, &:last-of-type': {
      borderLeft: 'none!important',
      borderRight: 'none!important',
    },
  },
}));

const StyledBoxName = styled(Box)({
  fontFamily: 'BT Curve, sans-serif',
  fontStyle: 'normal',
  fontWeight: 700,
  fontSize: '16px',
  lineHeight: '25px',
  alignItems: 'center',
  color: '#333333',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  width: '160px',
});



export {
  StyledTableRow, StyledTableCell, StyledTableContainer, StyledPaginationContainer, StyledEnhacedTableContainer, StyledBoxName
};
