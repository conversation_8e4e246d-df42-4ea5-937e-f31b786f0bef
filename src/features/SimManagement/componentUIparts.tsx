import {
  TableCell, styled, TableRow, TableContainer, useMediaQuery,
  Box,
} from '@mui/material';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import getCurrentThemeColors from 'core/utilities/getCurrentThemeColors';
import { IStyledTableCellProps } from './SimManagement.models';

const StyledTableContainer = styled(TableContainer)({
  paddingBottom: 30,
});

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '.MuiTableCell-root': {
    background: theme.palette.background.default,
  },
  '&:nth-child(even) .MuiTableCell-root': {
    background: styles.lightColor100,
  },
  '&:hover .MuiTableCell-root': {
    background: getCurrentThemeColors(theme.palette.primary.main)[100],
  },
}));

const StyledTableCell = styled(TableCell)<IStyledTableCellProps>(({
  bold = false, align = 'left', sticky = false, th = false,
}) => {
  const isSmallScreen = useMediaQuery('(max-width:1600px)');

  return ({
    fontSize: isSmallScreen ? 13 : 14,
    fontWeight: bold ? 700 : 400,
    textAlign: align,
    color: styles.darkColor500,
    padding: '7px 16px',
    height: 54,
    position: sticky ? 'sticky' : 'static',
    zIndex: 1,
    left: 0,
    overflow: 'hidden',
    borderLeft: 1,
    border: th ? `1px solid ${styles.lightColor200}` : 'none',
    borderWidth: th ? '1px 0' : 0,
    whiteSpace: 'nowrap',
    filter: sticky ? 'drop-shadow(8px 0px 8px rgba(140, 140, 140, 0.1))' : 'none',
  });
});

  const StyledPaginationContainer = styled(Box)<{ currentTheme: any }>(({ currentTheme  }) => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: '8px',
    backgroundColor: currentTheme?.bgLightPrimary || '#f5f1fa',
    padding: '7px 10px',
    borderRadius: '4px',
    '& .MuiButtonBase-root, & .MuiPaginationItem-root': {
      borderRadius: '4px',
      minWidth: '40px',
      minHeight: '40px',
      padding: '4px 10px',
      fontWeight: 700,
      '&.Mui-selected': {
        color: currentTheme?.primaryColor || '#5514b4',
        backgroundColor: currentTheme?.bgLightMediumPrimay|| '#ccb9e9',
        '&:hover': {
          backgroundColor: currentTheme?.bgLightMediumPrimay || '#ccb9e9',
        },
      },
      '&:hover': {
        backgroundColor: currentTheme?.actionBg || '#ebe3f6',
      },
    },
  }));

export {
  StyledTableRow, StyledTableCell, StyledTableContainer, StyledPaginationContainer
};
