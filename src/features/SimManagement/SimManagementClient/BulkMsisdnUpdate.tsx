import {
  Box,
  Button,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Dialog as MuiDialog,
  Select,
  styled,
  Table,
  TableBody,
  TableHead,
  TableRow,
  Tooltip,
  tooltipClasses,
  TooltipProps,
  Typography,
} from "@mui/material";
import CloseIcon from "assets/images/CloseIcon";
import {
  CSV_SUPPORTED_FORMATS,
  MB_CSV_FILE_SIZE,
  RESPONSE,
  SIMPROFILE_OPTIONS,
} from "core/utilities/constants";
import generateCSVFile from "core/utilities/generateCSVFile";
import { toastError, toastSuccess } from "core/utilities/toastHelper";

import { TOOL_TIP_BULK_MSISDN } from "features/constants";
import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import { AiOutlineCloudUpload } from "react-icons/ai";
import { GrCircleInformation, GrFormDown } from "react-icons/gr";
import Dialog from "shared/Dialog/Dialog";
import LatestDropZone from "shared/Dropzone/LatestDropZone";
import Loader from "shared/Loader";
import * as Yup from "yup";
import { totalMsisdn, uploadBulkMsisdn } from "../api.service";
import {
  StyledTableCell,
  StyledTableContainer,
  StyledTableRow,
} from "../componentUIparts";
import { MSISDN_TYPE_OPTIONS } from "../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants";
import "./BulkMsisdn.scss";
import { IFreeMsisdn } from "../IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models";
import { useAppContext } from "AppContextProvider";

interface IBulkMsisdnUpdate {
  open: boolean;
  onClose: () => void;
  accountDetails: any;
  reCallData: any;
  paramState: any;
}

const BulkMsisdnUpdate = ({
  open,
  onClose,
  accountDetails,
  reCallData,
  paramState,
}: IBulkMsisdnUpdate) => {
  const [msisdnList, setMsisdnList] = useState<any>({
    errorDetails: 0,
    totalDetails: 0,
    errorResults: [],
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errorModal, setErrorModal] = useState(false);
  const [loader, setLoader] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [totalRemainingCount, setTotalRemainingCount] = useState<IFreeMsisdn>({
    totalCount: 0,
    national: 0,
    international: 0,
  });
  const { currentTheme } = useAppContext();
  const [image, setImage] = useState<File>();

  const initialValues = {
    profileType: "",
    msisdnType: "",
    file: null,
  };

  const validationSchema = Yup.object().shape({
    profileType: Yup.string().required("SIM profile is Required"),
    msisdnType: Yup.string().required("MSISDN Type is Required"),
    file: Yup.mixed()
      .required("A file is required of valid columns and formats.")
      .test(
        "fileSize",
        "File too large",
        (value) => value && value.size <= MB_CSV_FILE_SIZE
      )
      .test(
        "fileFormat",
        "Unsupported Format",
        (value) => value && CSV_SUPPORTED_FORMATS.includes(value.type)
      ),
  });

  const formik = useFormik({
    validateOnBlur: false,
    validateOnChange: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();
      if (values.file && values.file !== null) {
        formData.append("file", values.file);
      }
      const paramData = {
        profileType: values.profileType,
        msisdnType: values.msisdnType,
      };

      try {
        setLoader(true);
        setErrorMessage(null);
        const { data } = await uploadBulkMsisdn(formData, paramData);
        setMsisdnList(data);
        toastSuccess(data?.message);
        setLoader(false);
        onClose();
      } catch (err: any) {
        setErrorMessage(err?.response?.data?.detail);
        setMsisdnList({
          errorDetails: 0,
          totalDetails: 0,
          errorResults: [],
        });
        setImage(undefined);
        formik.setFieldValue("file", null);
        toastError(
          err?.response?.data?.detail ||
            "Sorry, something went wrong while uploading MSISDN"
        );
        setLoader(false);
      }
    },
  });

  const fetchTotalRemainingCount = async () => {
    try {
      setLoader(true);
      const { data } = await totalMsisdn();
      setTotalRemainingCount(data);
      setLoader(false);
    } catch (error: any) {
      setLoader(false);
      console.log("error: ", error);
    }
  };

  const getUpdatedMsisdnOptions = (apiResponse) =>
    MSISDN_TYPE_OPTIONS.map((option) => ({
      ...option,
      count: apiResponse[option.value.toLowerCase()] || 0,
    }));

  const uploadImage = (fileData) => {
    formik.setFieldValue("file", fileData);
    setErrorMessage(null);
  };

  const removeImage = () => {
    formik.setFieldValue("file", null);
    setImage(undefined);
  };

  const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className ?? "" }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: 500,
    },
  });

  const handleErrorClose = () => {
    setErrorModal(false);
    onClose();
    const { page, pageSize, field, sort, search } = paramState;
    reCallData(page, pageSize, field, sort, search);
  };

  const oncloseModal = (event, reason) => {
    if (reason && (reason === "backdropClick" || reason === "escapeKeyDown")) {
      return;
    }
    handleErrorClose();
  };

  const downLoad = () => {
    const sims = msisdnList?.errorResults?.map((t) => ({
      MSISDN: t.msisdn,
      IMSI: t.imsi,
      ErrorType: t.issue,
    }));
    if (sims) {
      const dotIndex = image?.name?.lastIndexOf(".");
      const name =
        dotIndex !== -1
          ? `${image?.name?.substring(0, dotIndex)}_Invalid_MSISDN`
          : `${image?.name}_Invalid_MSISDN`;
      generateCSVFile(sims, name, setIsLoading, ";");
      handleErrorClose();
    }
  };

  useEffect(() => {
    if (open) {
      fetchTotalRemainingCount();
    }
  }, [open]);

  useEffect(() => {
    if (accountDetails) {
      // formik.setFieldValue('profileType', accountDetails?.simProfile?.ref);
    }
  }, [accountDetails]);

  const isCountAvailable =
    totalRemainingCount?.[formik.values.msisdnType.toLowerCase()] > 0;
  const checkInternationandSimprofile =
    formik.values.msisdnType === "INTERNATIONAL" &&
    formik.values.profileType === "VOICE_SMS_DATA";

  return (
    <Box>
      <Dialog
        title="Bulk Update IMSI(s)"
        open={open}
        onClose={() => onClose()}
        sx={{
          display: errorModal ? "none" : "block",
          "& .MuiDialogTitle-root": {
            p: 8,
          },
        }}
        footerchildren={
          <Box
            display="flex"
            alignItems="flex-start"
            gap="15px"
            justifyContent="space-between"
            mt="20px"
          >
            <Button
              component="button"
              sx={{ p: "0px 25px", minWidth: "110px" }}
              variant="contained"
              color="primary"
              disabled={
                !(formik.isValid && formik.dirty) ||
                checkInternationandSimprofile ||
                isLoading ||
                !isCountAvailable
              }
              onClick={(e) => {
                e.preventDefault();
                formik.handleSubmit();
              }}
              onMouseDown={(e) => e.preventDefault()}
            >
              {loader ? (
                <Box
                  sx={{
                    ".MuiBox-root": {
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    },
                  }}
                >
                  <Loader size={24} />
                </Box>
              ) : (
                "Confirm"
              )}
            </Button>
            <Button
              sx={{
                p: "0px 25px",
                backgroundColor: currentTheme?.actionBg || "#ebe3f6",
                border: "0px",
              }}
              variant="outlined"
              onClick={() => {
                onClose();
              }}
            >
              Cancel
            </Button>
          </Box>
        }
      >
        <Box sx={{ opacity: loader ? 0.5 : 1 }}>
          <form
            style={{ display: "table-caption" }}
            onSubmit={formik.handleSubmit}
          >
            <FormControl
              fullWidth
              className="sim-profile-select"
              sx={{
                margin: "5px 10px 20px 0px",
                "#sim-profile-label.MuiInputLabel-root": {
                  transform: "translate(20px, 9px) scale(1) !important",
                },
                "#sim-profile-label.MuiInputLabel-shrink": {
                  transform: "translate(14px, -6px) scale(0.75) !important",
                  padding: "0px 5px",
                  background: "white",
                },
                ".select-arrow-icon": {
                  position: "absolute",
                  right: "12px",
                  zIndex: 1,
                  polyline: {
                    stroke: "#696969",
                  },
                },
              }}
            >
              <InputLabel id="sim-profile-label">New SIM Profile</InputLabel>
              <Select
                IconComponent={() => (
                  <GrFormDown className="select-arrow-icon" size={21} />
                )}
                labelId="sim-profile-label"
                name="profileType"
                value={formik.values.profileType}
                onChange={formik.handleChange}
                error={
                  !!formik.errors.profileType && !!formik.touched.profileType
                }
              >
                {SIMPROFILE_OPTIONS.map((option) => (
                  <MenuItem key={option?.value} value={option?.value}>
                    {option?.title}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl
              fullWidth
              className="sim-profile-select"
              sx={{
                margin: !checkInternationandSimprofile
                  ? "5px 10px 20px 0px"
                  : "5px 10px 5px 0px",
                "#sim-profile-label.MuiInputLabel-root": {
                  transform: "translate(20px, 9px) scale(1) !important",
                },
                "#sim-profile-label.MuiInputLabel-shrink": {
                  transform: "translate(14px, -6px) scale(0.75) !important",
                  padding: "0px 5px",
                  background: "white",
                },
                ".select-arrow-icon": {
                  position: "absolute",
                  right: "12px",
                  zIndex: 1,
                  polyline: {
                    stroke: "#696969",
                  },
                },
              }}
            >
              <InputLabel id="sim-profile-label">MSISDN Type</InputLabel>
              <Select
                IconComponent={() => (
                  <GrFormDown className="select-arrow-icon" size={21} />
                )}
                labelId="sim-profile-label"
                name="msisdnType"
                value={formik.values.msisdnType}
                onChange={formik.handleChange}
                error={
                  !!formik.errors.msisdnType && !!formik.touched.msisdnType
                }
              >
                {getUpdatedMsisdnOptions(totalRemainingCount).map((option) => (
                  <MenuItem key={option?.value} value={option?.value}>
                    {option?.title}
                    &nbsp;
                    <span
                      style={{
                        // display: option?.value === '-2' ? 'none' : 'block',
                        color: option?.count > 0 ? "green" : "red",
                      }}
                    >
                      {option?.count > 0
                        ? `(Remaining ${option?.count})`
                        : "(Not Available)"}
                    </span>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {checkInternationandSimprofile && (
              <Typography
                variant="body1"
                fontWeight="600"
                fontSize={11}
                color={currentTheme?.primaryColor || "#5514B4"}
                mb={4}
              >
                {RESPONSE?.MSISDN_TYPE_CASE}
              </Typography>
            )}

            <Box display="flex" alignItems="center" marginBottom="10px">
              <Typography
                component="div"
                variant="body2"
                fontWeight={700}
                color="#333333"
              >
                Select File
              </Typography>
              <CustomWidthTooltip
                arrow
                placement="top"
                title={
                  <>
                    <Typography component="p" fontSize={14}>
                      Following criteria for successful processing:
                    </Typography>
                    <Typography>
                      <ul style={{ textIndent: "5px", fontSize: "13px" }}>
                        {TOOL_TIP_BULK_MSISDN?.map((item) => (
                          <li key={item}>
                            • &nbsp;
                            {item}
                          </li>
                        ))}
                      </ul>
                    </Typography>
                  </>
                }
              >
                <IconButton
                  sx={{
                    height: "0px",
                    "&:hover": {
                      backgroundColor: "none",
                    },
                  }}
                >
                  <GrCircleInformation />
                </IconButton>
              </CustomWidthTooltip>
            </Box>

            <LatestDropZone
              name="file"
              primaryColor="primary"
              formik={formik}
              uploadImage={uploadImage}
              setImage={setImage}
              image={image}
              dropzoneImg={<AiOutlineCloudUpload size={35} />}
              dropzoneText="Select CSV file with MSISDNs and IMSIs"
              removeAppImage={removeImage}
              description="File must be in .csv format"
              customErrorMessage={errorMessage}
            />
          </form>
        </Box>
      </Dialog>

      <MuiDialog
        open={errorModal}
        onClose={oncloseModal}
        aria-labelledby="edit-apartment"
      >
        <Box
          display="flex"
          justifyContent="space-between"
          alignItems="center"
          sx={{
            borderTop: "6px solid #5514b4",
            "& .MuiDialogTitle-root": {
              padding: "26px 32px !important",
            },
          }}
        >
          <DialogTitle id="display-dialog">
            <Typography variant="h3">Bulk Update IMSI(s)</Typography>
          </DialogTitle>
          <IconButton
            aria-label="close"
            sx={{
              marginRight: "32px",
            }}
            onClick={() => handleErrorClose()}
          >
            <CloseIcon />
          </IconButton>
        </Box>

        <DialogContent
          className="dialogueContent_sim"
          sx={{ padding: "0px 32px" }}
          style={{ overflow: "hidden" }}
        >
          <div>
            <Box
              sx={{
                backgroundColor: currentTheme?.bgLightPrimary || "#f5f1fa",
                display: "flex",
                padding: "10px",
                alignItems: "start",
                flexDirection: "column",
              }}
            >
              {/* <Typography variant="body1" component="p">
                <b>
                  {Number(msisdnList?.totalDetails) - Number(msisdnList?.errorDetails)}
                  MSISDN
                </b>
                out of
                <b>
                  {msisdnList?.totalDetails}
                </b>
                were successfully uploaded.
              </Typography> */}
              <Typography variant="body1" component="p">
                We have received your request.
              </Typography>
              {msisdnList?.errorDetails > 0 ? (
                <Typography variant="body1" component="p">
                  <Typography variant="body1" component="span" color="error">
                    <b>{msisdnList?.errorDetails} MSISDN</b>
                  </Typography>{" "}
                  in the file are invalid. You can fix them, replace or delete
                  from the file.
                </Typography>
              ) : null}
            </Box>
            {msisdnList?.errorDetails > 0 ? (
              <StyledTableContainer
                sx={{ maxHeight: "300px", marginTop: "10px" }}
              >
                <Table sx={{ border: "1px solid #E7E7EE" }}>
                  <TableHead>
                    <TableRow>
                      <StyledTableCell th bold>
                        MSISDN #
                      </StyledTableCell>
                      <StyledTableCell th bold>
                        IMSI
                      </StyledTableCell>
                      <StyledTableCell th bold>
                        Error Type
                      </StyledTableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {msisdnList?.errorResults?.map((err) => (
                      <StyledTableRow>
                        <StyledTableCell>{err?.msisdn}</StyledTableCell>
                        <StyledTableCell>{err?.imsi}</StyledTableCell>
                        <StyledTableCell>{err?.issue}</StyledTableCell>
                      </StyledTableRow>
                    ))}
                  </TableBody>
                </Table>
              </StyledTableContainer>
            ) : null}
          </div>
        </DialogContent>
        <DialogActions
          sx={{ padding: "0px 32px 32px", justifyContent: "flex-start" }}
        >
          <Box
            display="flex"
            alignItems="flex-start"
            gap="15px"
            justifyContent="space-between"
            mt="20px"
          >
            {msisdnList?.errorDetails > 0 && (
              <Button
                component="button"
                sx={{ p: "0px 25px", minWidth: "110px" }}
                variant="contained"
                color="primary"
                onClick={(e) => {
                  e.preventDefault();
                  downLoad();
                }}
                disabled={isLoading}
              >
                Download Invalid MSISDN
              </Button>
            )}
            <Button
              sx={{ p: "0px 25px", backgroundColor: "#ebe3f6", border: "0px" }}
              variant="outlined"
              onClick={() => handleErrorClose()}
            >
              OK
            </Button>
          </Box>
        </DialogActions>
      </MuiDialog>
    </Box>
  );
};

export default BulkMsisdnUpdate;
