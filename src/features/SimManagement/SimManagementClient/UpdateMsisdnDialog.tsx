import {
  Box, Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select, Typography,
} from '@mui/material';
import { RESPONSE, SIMPROFILE_OPTIONS } from 'core/utilities/constants';
import React, { FC, useEffect, useState } from 'react';
import { GrFormDown } from 'react-icons/gr';
import Dialog from 'shared/Dialog/Dialog';
import Loader from 'shared/Loader';
import { getFreeMsisdn } from '../api.service';
import MsisdnConfirm from './MsisdnConfirm';

import { MSISDN_TYPE_OPTIONS } from '../IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import { useAppContext } from 'AppContextProvider';

interface UpdateMsisdnDialogProps {
  open: boolean;
  onClose: () => void;
  rowData: any;
  reCallData: any;
  paramState: any;
}

const UpdateMsisdnDialog: FC<UpdateMsisdnDialogProps> = ({
  open, onClose, rowData, reCallData, paramState,
}) => {
  const [selectedProfile, setSelectedProfile] = useState('');
  const [stableRowData, setStableRowData] = useState(rowData);
  const [msisdnType, setMsisdnType] = useState('');
  const [loader, setLoader] = useState(false);
  const [msisdnNumber, setMsisdnNumber] = useState('');
  const [msisdnMessagesuccess, setMsisdnMessagesuccess] = useState('');
  const [msisdnMessageerror, setMsisdnMessageerror] = useState('');
  const [sureModal, setSureModal] = useState(false);

  const { currentTheme } = useAppContext();

  const filteredProfileOptions = SIMPROFILE_OPTIONS.filter(
    (option) => option.value !== rowData?.simProfile?.name
      && rowData?.msisdnFactor?.name !== 'INTERNATIONAL',
  );

  const checkInternationandSimprofile = msisdnType === 'INTERNATIONAL' && (selectedProfile === 'DATA_ONLY' ? false : selectedProfile === 'VOICE_SMS_DATA' || rowData?.simProfile?.name === 'VOICE_SMS_DATA');

  const fetchRandomMsisdn = async (): Promise<void> => {
    if (!msisdnType?.length || msisdnType === '-2') return;
    if (checkInternationandSimprofile) return;
    try {
      setLoader(true);
      const response = await getFreeMsisdn(msisdnType);
      setMsisdnNumber(response?.data?.result);
      setMsisdnMessagesuccess(`New MSISDN ${response?.data?.result}`);
      setMsisdnMessageerror('');
      setStableRowData(rowData);
      setLoader(false);
    } catch (error: any) {
      if (error?.response?.status === 404) {
        setMsisdnMessageerror(RESPONSE?.NO_MSISDN);
      } else {
        setMsisdnMessageerror(error?.response?.data?.detail);
      }
      setMsisdnMessagesuccess('');
      setMsisdnNumber('');
      setLoader(false);
    }
  };

  useEffect(() => {
    // fetchRandomMsisdn();
    setStableRowData(rowData);
  }, [selectedProfile]);

  useEffect(() => {
    fetchRandomMsisdn();
  }, [msisdnType, selectedProfile]);

  const CloseModal = () => {
    onClose();
    setMsisdnMessageerror('');
    setMsisdnMessagesuccess('');
    setSelectedProfile('');
    setMsisdnNumber('');
    setMsisdnType('');
  };

  useEffect(() => {
    if (open) {
      setSelectedProfile('-2');
      setMsisdnType('-2');
    }
  }, [open]);

  return (
    <Box>
      <Dialog
        title="Update IMSI(s)"
        open={open}
        onClose={() => {
          CloseModal();
        }}
        sx={{
          '& .MuiDialogTitle-root': {
            p: 8,
          },
        }}
        footerchildren={(
          <Box display="flex" alignItems="flex-start" gap="15px" justifyContent="space-between">
            <Button
              sx={{ p: '0px 25px', minWidth: '110px' }}
              variant="contained"
              color="primary"
              disabled={
                (!selectedProfile || selectedProfile === '-2')
                && (!msisdnType || msisdnType === '-2')
                || !!msisdnMessageerror
                || checkInternationandSimprofile
                || loader
              }
              onClick={() => {
                setSureModal(true);
                onClose();
              }}
            >
              Confirm
            </Button>
            <Button
              sx={{ p: '0px 25px', backgroundColor: currentTheme?.actionBg || '#ebe3f6', border: '0px' }}
              variant="outlined"
              onClick={() => {
                CloseModal();
              }}
            >
              Cancel
            </Button>
          </Box>
        )}
      >
        <Box>
          <Box display="grid" gridTemplateColumns="repeat(2, 1fr)" gap="2px 28px" textAlign="start">
            <Typography variant="body1">Selected MSISDN</Typography>
            <Typography variant="body1">Selected ICCID</Typography>

            <Typography variant="body1" fontWeight="bold">{rowData?.msisdn}</Typography>
            <Typography variant="body1" fontWeight="bold">{rowData?.iccid}</Typography>
          </Box>
          <Box display="grid" gridTemplateColumns="repeat(2, 1fr)" gap="2px 28px" mt={4} textAlign="start">
            <Typography variant="body1">Current SIM Profile</Typography>
            <Typography variant="body1">Current MSISDN Type</Typography>

            <Typography variant="body1" fontWeight="bold" textTransform="capitalize">{rowData?.simProfile?.name?.toLowerCase().replace(/_/g, ' ')}</Typography>
            <Typography variant="body1" fontWeight="bold" textTransform="capitalize">{rowData?.msisdnFactor?.name.toLowerCase()}</Typography>
          </Box>

          <FormControl
            fullWidth
            className="sim-profile-select"
            sx={{
              margin: '20px 10px 4px 0px',
              '#sim-profile-label.MuiInputLabel-root': {
                transform: 'translate(20px, 9px) scale(1) !important',
              },
              '#sim-profile-label.MuiInputLabel-shrink': {
                transform: 'translate(14px, -6px) scale(0.75) !important',
                padding: '0px 5px',
                background: 'white',
              },
              '.select-arrow-icon': {
                position: 'absolute',
                right: '12px',
                zIndex: 1,
                polyline: {
                  stroke: '#696969',
                },
              },
            }}
          >
            <InputLabel id="sim-profile-label">SIM Profile</InputLabel>
            <Select
              IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
              labelId="sim-profile-label"
              value={selectedProfile}
              onChange={(event) => setSelectedProfile(event.target.value)}
            >
              {[
                { title: 'No Changes', value: '-2' },
                ...filteredProfileOptions,
              ].map((option) => (
                <MenuItem key={option?.value} value={option?.value}>
                  {option?.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Typography
            variant="body1"
            fontWeight="600"
            fontSize={11}
            color={currentTheme?.primaryColor || "#5514B4"}
            mb={4}
            visibility={selectedProfile === '-2' ? 'visible' : 'hidden'}
          >
            SIM Profile will not be updated
          </Typography>

          <FormControl
            fullWidth
            className="sim-profile-select"
            sx={{
              margin: msisdnType ? '0px 10px 5px 0px' : '0px 10px 20px 0px',
              '#sim-profile-label.MuiInputLabel-root': {
                transform: 'translate(20px, 9px) scale(1) !important',
              },
              '#sim-profile-label.MuiInputLabel-shrink': {
                transform: 'translate(14px, -6px) scale(0.75) !important',
                padding: '0px 5px',
                background: 'white',
              },
              '.select-arrow-icon': {
                position: 'absolute',
                right: '12px',
                zIndex: 1,
                polyline: {
                  stroke: '#696969',
                },
              },
            }}
          >
            <InputLabel id="sim-profile-label">MSISDN Type</InputLabel>
            <Select
              IconComponent={() => (<GrFormDown className="select-arrow-icon" size={21} />)}
              labelId="sim-profile-label"
              value={msisdnType}
              onChange={(event) => { setMsisdnType(event.target.value); setMsisdnNumber(''); setMsisdnMessagesuccess(''); setMsisdnMessageerror(''); }}
            >
              {[
                { title: 'No Changes', value: '-2' },
                ...MSISDN_TYPE_OPTIONS].map((option) => (
                  <MenuItem key={option?.value} value={option?.value}>
                    {option?.title}
                  </MenuItem>
                ))}
            </Select>
          </FormControl>

          <Typography
            variant="body1"
            fontWeight="600"
            fontSize={11}
            color={currentTheme?.primaryColor || "#5514B4"}
            mb={4}
            visibility={msisdnType === '-2' ? 'visible' : 'hidden'}
            display={msisdnType === '-2' ? 'block' : 'none'}

          >
            MSISDN Number will not be updated
          </Typography>
          {
            loader
              ? (
                <Box display="flex" justifyContent="center" alignItems="center">
                  <Loader size={30} />
                </Box>
              ) : null
          }
          {msisdnMessagesuccess.length && !checkInternationandSimprofile
            ? (
              <Typography
                variant="body1"
                fontWeight="600"
                fontSize={11}
                color={ currentTheme?.greenColor || "#30B281"}
                mb={4}
              >
                {msisdnMessagesuccess}
              </Typography>
            ) : null}
          {
            msisdnMessageerror.length
              ? (
                <Typography
                  variant="body1"
                  fontWeight="600"
                  fontSize={11}
                  color={currentTheme?.redColor || "#EB5F64"}
                  mb={4}
                >
                  {msisdnMessageerror}
                </Typography>
              ) : null
          }
          {
            checkInternationandSimprofile && (
              <Typography
                variant="body1"
                fontWeight="600"
                fontSize={11}
                color={currentTheme?.primaryColor || "#5514B4"}
                mb={4}
                maxWidth={332}
              >
                {RESPONSE?.MSISDN_TYPE_CASE}
              </Typography>
            )
          }
        </Box>
      </Dialog>

      <MsisdnConfirm
        open={sureModal}
        onClose={() => {
          setSureModal(false);
          CloseModal();
        }}
        rowData={stableRowData}
        selectedProfile={selectedProfile}
        msisdnNumber={msisdnNumber}
        reCallData={reCallData}
        paramState={paramState}
        msisdnType={msisdnType}
      />
    </Box>
  );
};

export default UpdateMsisdnDialog;
