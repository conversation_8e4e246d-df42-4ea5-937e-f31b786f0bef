import { Box, useTheme } from '@mui/material';
import TabPanel from '@nv2/nv2-pkg-js-shared-components/lib/TabPanel';
import Tabs from '@nv2/nv2-pkg-js-shared-components/lib/Tabs';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import SimIcon from 'assets/images/SimIcon';
import CommonAuthwrapper from 'core/CommonAuthWrapper';
import { REPOSITORY, ROUTE_PERMISSION } from 'core/utilities/constants';
import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import FadeIn from 'shared/FadeIn';
import { AiOutlineCloudServer, AiOutlineMobile, AiOutlineShoppingCart, AiOutlineTag } from 'react-icons/ai';
import { IUser } from 'user.model';
import BulkOperationsTracker from './BulkOperationsTracker';
import { ITab } from './SimManagement.models';
import './SimManagement.scss';
import SimManagementClient from './SimManagementClient/SimManagementClient';
import { GetAuthorization } from 'PrivateRotes';
import SimOrdering from './SimOrdering';
import SIMTag from './SIMTag';

export const tabs: ITab = {
  'sim-management': 0,
  'bulk-operation': 1,
  'sim-order': 2,
  'sim-tag': 3
};

export const TabsName = {
  SIM_MANAGEMENT: 'sim-management',
  BULK_OPERATION: 'bulk-operation',
  SIM_ORDER: 'sim-order',
  SIM_TAG: 'sim-tag',
};

export const tabItemsConfig = [
  {
    name: 'SIM Management',
    icon: <SimIcon />,
  },
  {
    name: 'Bulk Operations Tracker',
    icon: <AiOutlineCloudServer />,
  },
  {
    name: 'Sim Orders',
    icon: <AiOutlineMobile />,
  },
  {
    name: 'TAGs',
    icon: <AiOutlineTag />,
  }
];

interface SimManagementClientProps {
  user: IUser | undefined,
}

const SimManagementClientTab = ({ user }: SimManagementClientProps) => {
  const theme = useTheme();
  const [tabIndex, setTabIndex] = useState(0);
  const [searchParams, setSearchParams] = useSearchParams();

  const tabItemsConfigs = [
    {
      name: 'SIM Management',
      icon: <SimIcon />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION.GET_RATE_PLANS, ROUTE_PERMISSION.GET_SIM_MANAGEMENT],
        [REPOSITORY.SIM_MANAGEMENT, REPOSITORY.RATE_PLAN]),
    },
    {
      name: 'Bulk Operations Tracker',
      icon: <AiOutlineCloudServer size={21} />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION?.VIEW_TRACE_DATA],
        [REPOSITORY?.AUDIT],
      ),
    },
    {
      name: 'SIM Orders',
      icon: <AiOutlineShoppingCart size={21} />,
      disabled: !GetAuthorization(
        [ROUTE_PERMISSION?.VIEW_ORDERS],
        [REPOSITORY?.SIM_ORDERS],
      ),
    },
    {
      name: 'TAGs',
      icon: <AiOutlineTag size={21} />,
      // disabled: !GetAuthorization(
      //   [],
      //   [],
      // ),
    },
  ];

  const selectedNewTab = async (event, newTabValue) => {
    setTabIndex(newTabValue);
    setSearchParams({ tab: Object.keys(tabs)[newTabValue] }, {
      replace: true,
    });
  };

  useEffect(() => {
    const tabFromQuery = searchParams.get('tab');
    const searchFromQuery = searchParams.get('search');
    if (tabFromQuery && tabs[tabFromQuery] !== tabIndex) {
      setTabIndex(tabs[tabFromQuery]);
      setSearchParams({ tab: tabFromQuery, search: searchFromQuery || '' }, {
        replace: true,
      });
    } else if (!tabFromQuery) {
      const defaultTabIndex = tabItemsConfigs?.findIndex((tab) => !tab.disabled);
      setSearchParams({ tab: Object.keys(tabs)[defaultTabIndex] }, {
        replace: true,
      });
    }
  }, [searchParams]);

  const TabComponent = [
    {
      components: <SimManagementClient user={user} />,
      permission: [ROUTE_PERMISSION.GET_RATE_PLANS, ROUTE_PERMISSION.GET_SIM_MANAGEMENT],
      repository: [REPOSITORY.SIM_MANAGEMENT, REPOSITORY.RATE_PLAN],
    },
    {
      components: <BulkOperationsTracker user={user} />,
      permission: [ROUTE_PERMISSION?.VIEW_TRACE_DATA],
      repository: [REPOSITORY?.AUDIT],
    },
    {
      components: <SimOrdering user={user} />,
      permission: [ROUTE_PERMISSION?.VIEW_ORDERS],
      repository: [REPOSITORY?.SIM_ORDERS],
    },
    {
      components: <SIMTag user={user} />,
      permission: [],
      repository: [],
    }
  ];

  return (
    <div
      className='sim-management-page'
      data-testid="sim-management-page"
      style={{ background: styles.lightColor50 }}
    >
      <Box
        sx={{
          '.MuiTabs-flexContainer': {
            '.Mui-disabled': {
              display: 'none',
            },
          },
        }}
      >
        <Tabs
          tabIndex={tabIndex}
          setTabIndex={setTabIndex}
          selectedNewTab={selectedNewTab}
          selectedTab={tabIndex}
          primaryColor={theme?.palette?.secondary?.main}
          tabItemsConfig={tabItemsConfigs}
        />
        {
          TabComponent?.map((item, index) => (
            <TabPanel index={index} value={tabIndex}>
              <FadeIn>
                <CommonAuthwrapper
                  permission={item?.permission}
                  repository={item?.repository}
                  isComponent
                >
                  {item?.components}
                </CommonAuthwrapper>
              </FadeIn>
            </TabPanel>
          ))
        }
      </Box>
    </div>
  );
};

export default SimManagementClientTab;
