import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  styled,
} from '@mui/material';
import { TooltipProps, tooltipClasses } from '@mui/material/Tooltip';
import React, { FC, useEffect, useState } from 'react';

import SimMicroIcon from 'assets/images/SimMicroIcon';
import SimNanoIcon from 'assets/images/SimNanoIcon';
import SimStandardIcon from 'assets/images/SimStandardIcon';
import getTotalRow from 'core/utilities/getTotalRowIMSIRangeSimModal';
import { getNumberWithCommas } from 'core/utilities/toMoneyFormat';
import { toastError, toastSuccess } from 'core/utilities/toastHelper';
import CreateAllocationModalRatePlan from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/CreateAllocationModalRatePlan';
import {
  StyledCreateAllocationModal,
  StyledSimTypeContainer,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/componentUIparts';
import { initialValues } from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/initialData';
import {
  ICreateAllocationModalForm1,
  IFreeMsisdn,
} from 'features/SimManagement/IMSIAllocations/IMSIAllocationsTopBar/CreateAllocationModal/models';
import { StyledCloseButton } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/IMSIRangeSimModalTable/IMSIRangeSimModalTableUI';
import { MSISDN_TYPE_OPTIONS, SIMPROFILE_OPTIONS, TotalProviderName } from 'features/SimManagement/IMSIRanges/IMSIRangesTopBar/IMSIRangeSimModal/constants';
import {
  IAccount,
  ICardsRemains,
  IRatePlan,
} from 'features/SimManagement/SimManagement.models';
import {
  createSimAllocationsByImport,
  getCardsRemains,
  getRatePlansById,
} from 'features/SimManagement/api.service';
import { CSV_SUPPORTED_FORMATS, TOOL_TIP_ITEMS } from 'features/constants';
import { FormikProps, useFormik } from 'formik';
import useMuiTableSearchParams from 'hooks/useMuiTableSearchParams';
import { AiOutlineCloudUpload } from 'react-icons/ai';
import { GrCircleInformation, GrClose, GrFormDown } from 'react-icons/gr';
import LatestDropZone from 'shared/Dropzone/LatestDropZone';
import Loader from 'shared/Loader';
import * as Yup from 'yup';
import './CreateAllocationModal.scss';
import { MB_CSV_FILE_SIZE } from 'core/utilities/constants';
import SimMFF2Icon from 'assets/images/SimMff2Icon';
import SimMff2EuiccIcon from 'assets/images/SimMff2EuiccIcon';
import { useAppContext } from 'AppContextProvider';

interface ICreateAllocationModalProps {
  handleClose: (event?: any, reason?: any) => void;
  accounts: IAccount[];
  setImage: (event?: any, reason?: any) => void;
  ratePlanLoading: boolean;
  setResponseData: (event?: any, reason?: any) => void;
  setOpenError: (event?: any, reason?: any) => void;
  setOpenSuccess: (event?: any, reason?: any) => void;
  image: File | undefined;
  fetchAccounts: () => void;
  setParentLoading: (event?: any, reason?: any) => void;
  totalRemainingCount: IFreeMsisdn;
}
export const emptyIMSIRange = {
  title: 'No available ranges',
  id: 1,
  disabled: true,
};
const validationSchema = Yup.object().shape({
  accountId: Yup.string().required('Required'),
  ratePlanId: Yup.string().required('Rateplan Required '),
  allocationReference: Yup.string().required('Required'),
  simProfile: Yup.string().required('SIM Profile is Required'),
  msisdnFactor: Yup.string().required('MSISDN Type is Required'),
  file: Yup.mixed()
    .required('A file is required of valid columns and formats.')
    .test(
      'fileSize',
      'File too large',
      (value) => value && value.size <= MB_CSV_FILE_SIZE,
    )
    .test(
      'fileFormat',
      'Unsupported Format',
      (value) => value && CSV_SUPPORTED_FORMATS.includes(value.type),
    ),
});

const CreateAllocationModal: FC<ICreateAllocationModalProps> = ({
  accounts,
  handleClose,
  setImage,
  setResponseData,
  setOpenError,
  ratePlanLoading,
  setOpenSuccess,
  image,
  setParentLoading,
  fetchAccounts,
  totalRemainingCount,
}) => {
  const [loading, setLoading] = useState(false);
  const [cardRemains, setCardRemains] = useState<ICardsRemains[]>([]);
  const handleOpen = () => setOpenError(true);
  const { getParamsFromUrl } = useMuiTableSearchParams();
  const [ratePlans, setRatePlans] = useState<IRatePlan[]>();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { currentTheme } = useAppContext();
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    page, pageSize, field, sort, search,
  } = getParamsFromUrl();

  const formik: FormikProps<ICreateAllocationModalForm1> = useFormik({
    validateOnBlur: false,
    validateOnChange: true,
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      const formData = new FormData();
      if (values.file && values.file !== null) {
        formData.append('file', values.file);
      }
      const paramData = {
        params: {
          allocationReference: values.allocationReference,
          accountId: values.accountId,
          ratePlanId: values.ratePlanId,
          simProfile: values.simProfile,
          msisdnFactor: values.msisdnFactor,
        },
      };

      try {
        setLoading(true);
        setParentLoading(true);
        setErrorMessage(null);
        const res = await createSimAllocationsByImport(formData, paramData);
        setLoading(false);
        setResponseData(res.data);
        handleClose(null);
        fetchAccounts();
        toastSuccess(res?.data?.message);
        setParentLoading(false);
      } catch (err: any) {
        setErrorMessage(err?.response?.data?.detail);
        if (err && err?.response?.status === 422) {
          setResponseData(err?.response?.data);
          handleClose(null);
        } else {
          toastError(err?.response?.data?.detail);
          setResponseData('');
        }
        formik.setFieldValue('file', null);
        setImage(undefined);
        setLoading(false);
        setParentLoading(false);
      }
    },
  });

  useEffect(() => {
    const fetchRatePlans = async () => {
      try {
        setParentLoading(true);
        const ratePlansResponse = await getRatePlansById(formik?.values.accountId);
        const sortedRatePlans = ratePlansResponse?.data?.ratePlans?.sort(
          (a, b) => Number(b?.isDefault) - Number(a?.isDefault),
        );
        setRatePlans(sortedRatePlans);
        setParentLoading(false);
      } catch (err) {
        toastError(err);
        setParentLoading(false);
      }
    };
    if (formik?.values?.accountId) {
      fetchRatePlans();
    }
  }, [formik?.values?.accountId]);

  const uploadImage = (fileData) => {
    formik.setFieldValue('file', fileData);
    setErrorMessage(null);
  };
  const removeImage = () => {
    formik.setFieldValue('file', null);
    setImage(undefined);
  };
  const onSelectRatePlan = (ratePlanId: number | undefined) => {
    formik.setFieldValue('ratePlanId', ratePlanId);
  };

  const onLoadTotalRemaining = async () => {
    try {
      const cards = await getCardsRemains();

      const totalRow = getTotalRow(cards.data);
      setCardRemains([...cards.data, totalRow]);
    } catch (e) {
      toastError('Failed to upload Total Remaining SIMs in the Stock.');
    }
  };
  useEffect(() => {
    onLoadTotalRemaining();
  }, []);

  const totalRow = cardRemains.find(
    ({ provider }) => provider === TotalProviderName,
  );

  const CustomWidthTooltip = styled(({ className, ...props }: TooltipProps) => (
    <Tooltip {...props} classes={{ popper: className ?? '' }} />
  ))({
    [`& .${tooltipClasses.tooltip}`]: {
      maxWidth: 500,
    },
  });

  const getUpdatedMsisdnOptions = (apiResponse) => MSISDN_TYPE_OPTIONS.map((option) => ({
    ...option,
    count: apiResponse[option.value.toLowerCase()] || 0,
  }));

  return (
    <StyledCreateAllocationModal
      data-testid="create-allocation-modal"
      className="create-allocation-modal"
      sx={{
        padding: '26px 0px 32px 32px',
      }}
    >
      <div className="create-allocation-modal__header" style={{ paddingRight: '32px', marginBottom: '5px' }}>
        <Typography variant="h2" component="h2">
          Allocate IMSI Range
        </Typography>
        <StyledCloseButton
          data-testid="create-allocation-modal_close-button"
          type="button"
          onClick={handleClose}
        >
          <GrClose size={21} />
        </StyledCloseButton>

      </div>
      <div className="create-allocation-modal__body">
        <form
          onSubmit={formik.handleSubmit}
          style={{
            maxHeight: 'calc(100vh - 200px)',
            overflow: 'auto',
            padding: '10px 32px 0px 0px',
          }}
          className="scrollBar"
        >
          <FormControl className="create-allocation-modal__body_form-control">
            <InputLabel>Account</InputLabel>
            <Select
              IconComponent={() => (
                <GrFormDown className="select-arrow-icon" size={21} />
              )}
              label="Account"
              name="accountId"
              onChange={formik.handleChange}
              value={formik.values.accountId}
              error={!!formik.errors.accountId && !!formik.touched.accountId}
            >
              {accounts?.map((account) => (
                <MenuItem key={account.id} value={account.id}>
                  {account?.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <CreateAllocationModalRatePlan
            ratePlans={ratePlans}
            selectedAccountId={formik.values.accountId}
            onChange={formik.handleChange}
            value={formik.values.ratePlanId}
            onSelectRatePlan={onSelectRatePlan}
            formik={formik}
          />
          <TextField
            autoComplete="off"
            className="create-allocation-modal__body_form-control"
            label="Allocation Reference"
            variant="outlined"
            name="allocationReference"
            onChange={formik.handleChange}
            value={formik.values.allocationReference}
            error={
              !!formik.errors.allocationReference
              && !!formik.touched.allocationReference
            }
            inputProps={{ maxLength: 60 }}
          />
          <FormControl className="create-allocation-modal__body_form-control">
            <InputLabel>SIM Profile</InputLabel>
            <Select
              IconComponent={() => (
                <GrFormDown className="select-arrow-icon" size={21} />
              )}
              label="SIM Profile"
              name="simProfile"
              onChange={formik.handleChange}
              value={formik.values.simProfile}
              error={!!formik.errors.simProfile && !!formik.touched.simProfile}
            >
              {SIMPROFILE_OPTIONS?.map((profile) => (
                <MenuItem key={profile.value} value={profile.value}>
                  {profile?.title}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <FormControl className="create-allocation-modal__body_form-control">
            <InputLabel>MSISDN Type</InputLabel>
            <Select
              IconComponent={() => (
                <GrFormDown className="select-arrow-icon" size={21} />
              )}
              label="MSISDN Type"
              name="msisdnFactor"
              onChange={formik.handleChange}
              value={formik.values.msisdnFactor}
              error={!!formik.errors.msisdnFactor && !!formik.touched.msisdnFactor}
            >
              {getUpdatedMsisdnOptions(totalRemainingCount)?.map((msisdn) => (
                <MenuItem key={msisdn.value} value={msisdn.value}>
                  {msisdn?.title}
                  &nbsp;
                  <span style={{ color: msisdn?.count > 0 ? 'green' : 'red' }}>
                    {msisdn?.count > 0 ? `(Remaining ${msisdn?.count})` : '(Not Available)'}
                  </span>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <Box display="flex" alignItems="center" marginBottom="10px">
            <Typography
              component="div"
              variant="body2"
              fontWeight={700}
              color="#333333"
            >
              Upload IMSI(s)
            </Typography>
            <CustomWidthTooltip
              arrow
              placement="top"
              title={(
                <>
                  <Typography component="p" fontSize={14}>
                    Following criteria for successful processing:
                  </Typography>
                  <Typography>
                    <ul style={{ textIndent: '5px', fontSize: '13px' }}>
                      {TOOL_TIP_ITEMS?.map((item) => (
                        <li key={item}>
                          •
                          &nbsp;
                          {item}
                        </li>
                      ))}
                    </ul>
                  </Typography>
                </>
              )}
            >
              <IconButton
                sx={{
                  height: '0px',
                  '&:hover': {
                    backgroundColor: 'none',
                  },
                }}
              >
                <GrCircleInformation />
              </IconButton>
            </CustomWidthTooltip>
          </Box>

          <LatestDropZone
            name="file"
            primaryColor="primary"
            formik={formik}
            uploadImage={uploadImage}
            setImage={setImage}
            image={image}
            dropzoneImg={<AiOutlineCloudUpload size={35} />}
            dropzoneText="Select CSV file with IMSI List"
            removeAppImage={removeImage}
            description="File must be in .csv format"
            customErrorMessage={errorMessage}
          />

          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-between"
            marginBottom="20px"
          >
            <Box display="flex" flexDirection="column" alignItems="flex-start">
              <Typography
                variant="body2"
                sx={{
                  backgroundColor: '#E6F6F0',
                  padding: '8px',
                  borderRadius: '4px',
                }}
              >
                Standard (2FF)
              </Typography>
              <StyledSimTypeContainer isSelected={false}>
                <SimStandardIcon />
                <Box display="flex" flexDirection="column">
                  <Typography variant="body2">Remaining</Typography>
                  <Typography
                    className="standard2ff"
                    component="div"
                    variant="body2"
                    fontWeight={800}
                  >
                    {getNumberWithCommas(totalRow?.standard)}
                  </Typography>
                </Box>
              </StyledSimTypeContainer>
            </Box>
            <Box display="flex" flexDirection="column" alignItems="flex-start">
              <Typography
                variant="body2"
                sx={{
                  backgroundColor: '#E1F2FA',
                  padding: '8px',
                  borderRadius: '4px',
                }}
              >
                Micro (3FF)
              </Typography>
              <StyledSimTypeContainer isSelected={false}>
                <SimMicroIcon />
                <Box display="flex" flexDirection="column">
                  <Typography variant="body2">Remaining</Typography>
                  <Typography
                    component="div"
                    variant="body2"
                    className="micro"
                    fontWeight={800}
                  >
                    {getNumberWithCommas(totalRow?.micro)}
                  </Typography>
                </Box>
              </StyledSimTypeContainer>
            </Box>
            <Box display="flex" flexDirection="column" alignItems="flex-start">
              <Typography
                variant="body2"
                sx={{
                  backgroundColor: '#FEEEEC',
                  padding: '8px',
                  borderRadius: '4px',
                }}
              >
                Nano (4FF)
              </Typography>
              <StyledSimTypeContainer isSelected={false}>
                <SimNanoIcon />
                <Box display="flex" flexDirection="column">
                  <Typography variant="body2">Remaining</Typography>
                  <Typography
                    component="div"
                    variant="body2"
                    className="nano"
                    fontWeight={800}
                  >
                    {getNumberWithCommas(totalRow?.nano)}
                  </Typography>
                </Box>
              </StyledSimTypeContainer>
            </Box>
          </Box>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="space-evenly"
            marginBottom="20px"
          >
            <Box display="flex" flexDirection="column" alignItems="flex-start">
              <Typography
                variant="body2"
                sx={{
                  backgroundColor: '#fDD5CE',
                  padding: '8px',
                  borderRadius: '4px',
                }}
              >
                eSIM (MFF2)
              </Typography>
              <StyledSimTypeContainer isSelected={false}>
                <SimMFF2Icon />
                <Box display="flex" flexDirection="column">
                  <Typography variant="body2">Remaining</Typography>
                  <Typography
                    component="div"
                    variant="body2"
                    className="nano"
                    fontWeight={800}
                  >
                    {getNumberWithCommas(totalRow?.esim_mff2)}
                  </Typography>
                </Box>
              </StyledSimTypeContainer>
            </Box>
            <Box display="flex" flexDirection="column" alignItems="flex-start">
              <Typography
                variant="body2"
                sx={{
                  backgroundColor: '#E7E7E7',
                  padding: '8px',
                  borderRadius: '4px',
                }}
              >
                eSIM (MFF2 eUICC)
              </Typography>
              <StyledSimTypeContainer isSelected={false}>
                <SimMff2EuiccIcon />
                <Box display="flex" flexDirection="column">
                  <Typography variant="body2">Remaining</Typography>
                  <Typography
                    component="div"
                    variant="body2"
                    className="nano"
                    fontWeight={800}
                  >
                    {getNumberWithCommas(totalRow?.esim_mff2_euicc)}
                  </Typography>
                </Box>
              </StyledSimTypeContainer>
            </Box>
          </Box>
        </form>
        <div className="create-allocation-modal__body_buttons">
          <Button
            disabled={!(formik.isValid && formik.dirty)}
            onClick={(e) => {
              e.preventDefault();
              formik.handleSubmit();
            }}
            component="button"
            sx={{
              p: '0px 25px', minWidth: '110px', textTransform: 'uppercase', fontSize: '12px !important',
            }}
            variant="contained"
            color="primary"
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-allocation-modal__body_buttons_confirm"
          >
            Confirm
          </Button>
          <Button
            // className="create-allocation-modal__body_cancel-button"
            sx={{
              p: '0px 25px', backgroundColor: currentTheme?.actionBg || '#ebe3f6', border: '0px', textTransform: 'uppercase', fontSize: '12px !important',
            }}
            variant="outlined"
            onClick={handleClose}
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-allocation-modal__body_buttons_cancel"
          >
            Cancel
          </Button>
        </div>
      </div>
      {(ratePlanLoading || loading) && (
        <div
          className="create-allocation-modal__loader"
          data-testid="create-allocation-modal__loader"
        >
          <Loader staticColor={currentTheme?.bgLightPrimary || "#f5f1fa"} size={60} />
        </div>
      )}
    </StyledCreateAllocationModal>
  );
};

export default CreateAllocationModal;
