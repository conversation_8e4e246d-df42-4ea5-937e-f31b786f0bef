import {
  Box,
  Button,
  Table, TableBody, TableHead, TableRow, Typography,
} from '@mui/material';
import generateCSVFile from 'core/utilities/generateCSVFile';
import { IResponse } from 'features/SimManagement/SimManagement.models';
import { StyledTableCell, StyledTableContainer, StyledTableRow } from 'features/SimManagement/componentUIparts';
import React, { FC, useState } from 'react';
import { GrClose } from 'react-icons/gr';
import { StyledCreateAllocationModal } from '../CreateAllocationModal/componentUIparts';
import { useAppContext } from 'AppContextProvider';

interface IMSIRangeValidateErrorModalProps {
  responseData: IResponse | null;
  setResponseData: (any) => void;
  handleClose: (event?: any, reason?: any) => void;
  fileName: string | undefined;
  setImage: (event?: any, reason?: any) => void;
  fetchAccounts: () => void;
}

const IMSIRangeValidateErrorModal: FC<IMSIRangeValidateErrorModalProps> = ({
  responseData,
  setResponseData,
  handleClose,
  fileName,
  setImage,
  fetchAccounts,

}) => {
  const { currentTheme } = useAppContext();

  const [isLoading, setIsLoading] = useState(false);
  const closePopup = () => {
    setResponseData({});
    setImage(undefined);
    handleClose();
    fetchAccounts();
  };
  const downLoad = () => {
    const sims = responseData?.results?.map((t) => ({ IMSI: t.sim, ErrorType: t.issue }));
    if (sims) {
      const dotIndex = fileName?.lastIndexOf('.');
      const name = dotIndex !== -1
        ? `${fileName?.substring(0, dotIndex)}_Invalid_IMSI`
        : `${fileName}_Invalid_IMSI`;
      generateCSVFile(sims, name, setIsLoading, ';');
      closePopup();
    }
  };
  const totalSIM = responseData?.totalSIM ?? 0;
  const errorSIM = responseData?.errorSIM ?? 0;
  const successSIM = totalSIM - errorSIM;

  return (
    <StyledCreateAllocationModal
      data-testid="create-allocation-modal"
      className="create-allocation-modal"
    >

      <div className="create-allocation-modal__header">
        <Typography variant="h2" component="h2" sx={{ display: 'flex', paddingRight: '10px' }}>
          Allocate IMSI Range
        </Typography>
        <button
          data-testid="create-allocation-modal_close-button"
          type="button"
          onClick={closePopup}
        >
          <GrClose size={21} />
        </button>
      </div>
      <div className="create-allocation-modal__body">

        <Box sx={{
          backgroundColor: currentTheme?.bgLightPrimary || "#f5f1fa",
          display: 'flex',
          padding: '10px',
          alignItems: 'center',
        }}
        >
          <Typography
            variant="body1"
          >
            <strong>
              {successSIM}
              {' '}
              IMSIs
            </strong>
            {' '}
            out of
            {' '}
            <strong>{totalSIM}</strong>
            {' '}
            were successfully allocated.
            <br />
            <strong style={{ color: '#DA5454' }}>
              {errorSIM}
              {' '}
              IMSIs
            </strong>
            {' '}
            &nbsp;in the file are invalid. You can fix them, replace or delete
            from the file.
          </Typography>
        </Box>
        <br />
        <StyledTableContainer sx={{ maxHeight: '435px' }}>
          <Table sx={{ border: '1px solid #E7E7EE' }}>
            <TableHead>
              <TableRow>
                <StyledTableCell th bold>IMSI #</StyledTableCell>
                <StyledTableCell th bold>Error Type</StyledTableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {responseData?.results?.map((err) => (
                <StyledTableRow>
                  <StyledTableCell>{err.sim}</StyledTableCell>
                  <StyledTableCell>{err.issue}</StyledTableCell>
                </StyledTableRow>
              ))}
            </TableBody>
          </Table>
        </StyledTableContainer>
        <div className="create-allocation-modal__body_buttons">
          <Button
            onClick={() => downLoad()}
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-allocation-modal__body_buttons_confirm"
            component="button"
            variant="contained"
            color="primary"
            disabled={isLoading}
            sx={{
              p: '0px 25px', minWidth: '110px', fontSize: '12px !important',
            }}
          >
            Download Invalid IMSIs
          </Button>
          <Button
            className="create-allocation-modal__body_cancel-button"
            onClick={closePopup}
            variant="outlined"
            onMouseDown={(e) => e.preventDefault()}
            data-testid="create-allocation-modal__body_buttons_cancel"
            sx={{
              p: '0px 25px', backgroundColor: '#ebe3f6', border: '0px', fontSize: '12px !important',
            }}
          >
            OK
          </Button>
        </div>
      </div>
    </StyledCreateAllocationModal>
  );
};

export default IMSIRangeValidateErrorModal;
