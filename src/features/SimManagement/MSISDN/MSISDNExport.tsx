import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import Loader from 'shared/Loader';
import { GridExport } from 'assets/images/ImagePlaceholder';
import { useAppContext } from 'AppContextProvider';

interface MSISDNActionsProps {
  rowCount: number,
  isLoading: boolean,
  onExportCSVFile: () => void,
}

const MSISDNExport = ({
  rowCount, isLoading, onExportCSVFile,

}: MSISDNActionsProps) => {
  const { currentTheme } = useAppContext();

  return (
    <Tooltip title={rowCount !== 0 ? `Export ${rowCount} Record(s)` : ''} arrow placement="top">

      <IconButton
        onClick={onExportCSVFile}
        disabled={isLoading || !rowCount}
        data-testid="Export"
        sx={{
          '&:hover svg': {
            fill: currentTheme?.primaryColor
          },
          '&:hover path': {
            strokeWidth: '1px',
            stroke: 'unset',
            fill: currentTheme?.primaryColor
          },
        }}
      >
        {
          isLoading
            ? <Loader size={21} />
            : <GridExport />
        }
      </IconButton>

    </Tooltip>
  )
};

MSISDNExport.defaultProps = {

};

export default MSISDNExport;
