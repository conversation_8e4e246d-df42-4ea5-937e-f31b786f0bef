export const ACCOUNT_IDS = [
  {
    value: 1,
    name: 'account-1',
  },
  {
    value: 2,
    name: 'account-2',
  },
  {
    value: 3,
    name: 'account-3',
  },
];

export const TOOL_TIP_ITEMS = [
  'The file must include the IMSI column.',
  "Filename begins with '2FF_','3FF_', '4FF_', 'eSIM_MFF2_' or 'eSIM_MFF2_eUICC_'.",
  'File size does not exceed 5MB.',
];

export const TOOL_TIP_RANGE = [
  'The file must include ICCID, IMSI and MSISDN columns.',
  'File size does not exceed 5MB.',
];

export const getTooltipMessages = (hasEID: boolean): string[] => [
  `The file must include ICCID, IMSI, MSISDN${hasEID ? ' and EID' : ''} columns.`,
  'File size must not exceed 5MB.',
];

export const TOOL_TIP_UPLOAD_MSISDN = [
  'The file must include the MSISDN column.',
  'File size does not exceed 5MB.',
];

export const TOOL_TIP_BULK_MSISDN = [
  'The file must include the MSISDN and IMSI columns.',
  'File size does not exceed 5MB.',
  'File must be uploaded with the same MSISDN Type and SIM profile group.',
];

export const RANGE_REFERENCES = [
  {
    value: 1,
    name: 'Example',
  },
  {
    value: 2,
    name: 'Example-2',
  },
  {
    value: 3,
    name: 'Example-3',
  },
];

export const TOASTS = {
  GET_IMSI_RANGES_ERROR: 'No available IMSI Ranges found. Try reloading the page.',
  GET_IMSI_ALLOCATIONS_ERROR: 'No available IMSI Allocations found. Try reloading the page.',
  CREATE_ALLOCATION_SUCCESS: 'IMSI Allocation was created successfully!',
  CREATE_ALLOCATION_ERROR: 'Failed to create IMSI Allocation. Please, try again.',
  REFRESH_RATE_PLAN_DATA_ERROR: 'Failed to refresh data. Please, try again.',
  EXPORT_SIM_MANAGEMENT_CLIENT_SUCCESS: 'Data is successfully exported!',
  EXPORT_SIM_MANAGEMENT_CLIENT_ERROR: 'Failed to export data. Please, try again',
  SUCCESS_DATA_SESSION_DISCONNECT:'Data Session POD disconnected successfully!',
  FAILURE_DATA_SESSION_DISCONNECT: 'Failed to disconnect Data Session POD. Please, try again!',
  SUCCESS_FLUSH_SIM_STATE: 'Cancel Location request is sent successfully!',
  FAILURE_FLUSH_SIM_STATE: 'Failed to sent Cancel Location request. Please, try again!',
  SUCCESS_GET_TAG_LIST: 'Tag list is successfully fetched!',
  FAILURE_GET_TAG_LIST: 'Failed to fetch tag list. Please, try again!',
};

export const CSV_FILE_SIZE = 1048576;
export const CSV_SUPPORTED_FORMATS = ['text/csv', 'application/vnd.ms-excel', 'application/csv'];

export enum SIM_ACTIONS {
  FLUSH = 'FLUSH',
  APN = 'APN',
  POD = 'POD',
  SMS = 'SMS',
}

export const flushSimStateMessage = 'After confirmation, a Cancel Location request will be sent.'
export const DataDisconnectSessionMessage = 'After confirmation, Data Session POD will be disconnected.';
export const SIMTagsValidationMessage = 'This tag is linked to the SIM(s) and cannot be removed. To delete this tag, please first unlink it from the SIM(s).'
export const SIMTagsMessage = 'After confirmation the tag will be deleted.'

export const sortSimActionNames = {
  createdAt: 'created_at',
  clientIp: 'client_ip',
  action: 'action',
  createdBy: 'created_by',
}

export const RESPONSE = {
  GET_SIMACTION_EMPTY: 'There is no SIM actions available.',
};
