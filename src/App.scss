@import "assets/styles/reset";

body {
  background-color: $app-background-color;
  font-size: 14px;

  .ps__rail-y {
    opacity: 0.6 !important;
    z-index: 10;
  }

  .scrollBar::-webkit-scrollbar {
    width: 7px;
    height: 7px;
  }

  .scrollBar::-webkit-scrollbar-thumb {
    // box-shadow: inset 0 0 6px rgba(163, 163, 177, 0.9);
    background-color: #dedee6;
    padding-inline: 2px;
    border-radius: 20px;
  }
}

.Toastify {
  &__toast-icon {
    width: 28px;

    svg {
      width: 28px;
      height: 28px;
    }
  }

  &__toast {
    padding-right: 17px;
    font-weight: 700;
    font-size: 14px;
    color: #1c1c28;

    &--error {
      border-left: 3px solid #eb5f64;
      background-color: #fce8e9;

      svg {
        fill: #eb5f64;
      }
    }

    &--info {
      border-left: 3px solid #5514b4;
      background-color: var(--action-bg, #ebe3f6);
      font-size: 14px;

      svg {
        fill: #5514b4;
      }
    }

    &--success {
      border-left: 3px solid #30b281;
      background-color: #e2f4ed;
      font-weight: 700;
      font-size: 14px;
      color: #1c1c28;

      svg {
        fill: #30b281;
      }
    }

    svg {
      width: 28px;
      height: 28px;
    }
  }

  .toastify-close-button {
    margin: auto;

    svg {
      width: 20px;
      height: 20px;
      fill: #696969;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.sim-management-app {
  height: 100%;
}
