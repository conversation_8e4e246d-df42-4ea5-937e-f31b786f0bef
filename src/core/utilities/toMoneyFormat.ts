import { isNaN } from 'lodash';
import { BYTES_TO_MB_DIVIDER } from './constants';

const toMoneyFormat = (amount: number | string, currency?: string): string => {
  if (isNaN(amount)) return '0.00';

  const formatedAmount = Intl.NumberFormat('en-US').format(+amount);

  if (formatedAmount === 'NaN') return '0.00';

  let outputAMount = '';

  if (formatedAmount.includes('.')) {
    const amountParts = formatedAmount.split('.');
    outputAMount = amountParts[1].length === 1 ? `${formatedAmount}0` : `${formatedAmount}`;
  } else {
    outputAMount = `${formatedAmount}.00`;
  }

  return `${outputAMount}${currency ? `, ${currency}` : ''}`;
};

export const makeNumberFormat = (num: number) => {
  if (Number.isNaN(num)) return '0';
  return num?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const bytesToMb = (bytes: number) => {
  if (bytes === 0) return '0.00';
  return (bytes / BYTES_TO_MB_DIVIDER).toFixed(2);
};
export const numberWithCommas = (x) => x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

export const getNumberWithCommas = (n: string | number | undefined | null): string => {
  if (!n) return '0';
  const number = numberWithCommas(n);
  return (
    number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')
  );
};

export const getNumberWithComasEsacpe = (n:number |string |undefined | null, scale = 2) => {
  if (n === undefined || n === null) return '0';
  const number = typeof n === 'string' ? parseFloat(n) : n;

  if (isNaN(number)) {
    const numberZero = 0;
    return numberZero.toFixed(scale);
  }
  const hasDecimal = number % 1 !== 0;
  if (hasDecimal) {
    const formattedDecimal = number.toFixed(scale).split('.')[1];
    return `${(number.toFixed(scale).split('.')[0]).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}.${formattedDecimal}`;
  }
  const formattedDecimal = number.toFixed(scale).split('.')[1];
  return `${number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}.${formattedDecimal}`;
};

export const calCulatePerCentEEUsage = (valueParam = 0, usage = 0) => {
  if (valueParam === 0 || usage === 0) {
    return '0.00';
  }
  const eeUsageMbPercent = (valueParam / usage) * 100;
  const eePercentage = getNumberWithComasEsacpe(eeUsageMbPercent);
  return eePercentage;
};

export default toMoneyFormat;
