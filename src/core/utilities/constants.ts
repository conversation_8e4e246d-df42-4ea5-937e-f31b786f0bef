export const FULL_DATE_FORMAT = 'DD-MM-YYYY';
export const SHORT_DATE_FORMAT = 'MM-YYYY';

// select options
export const CURRENCY_OPTIONS = [{ value: 'GBP', title: 'GBP' }];

export const COUNTRY_CODES_OPTIONS = [{ value: '+44', title: '+44' }];

export const COUNTRY_OPTIONS = [{ value: 'GB', title: 'United Kingdom' }];

export const SIMPROFILE_OPTIONS = [{
  value: 'DATA_ONLY',
  title: 'Data Only',
}, {
  value: 'VOICE_SMS_DATA',
  title: 'Voice/SMS/Data',
}];

export const CSV_FILE_SIZE = 1048576;

export const MB_CSV_FILE_SIZE = 1048576 * 5;

export const CSV_SUPPORTED_FORMATS = ['text/csv', 'application/vnd.ms-excel', 'application/csv'];

export const CUSTOMER_ACCOUNT_STATUS = [{ value: 'ACTIVE', title: 'Active' }];

export const NO_CREATED_RATE_PLANS = [{ value: undefined, title: 'No created Rate Plans' }];

export const INDUSTRY_VERTICAL_OPTIONS = [
  { value: 'RETAIL_OR_MARKETING', title: 'Retail or marketing' },
];

export const SALES_CHANNEL_OPTIONS = [
  { value: 'Indirect', title: 'Indirect' },
  { value: 'Internal', title: 'Internal' },
  { value: 'Trial', title: 'Trial' },
  { value: 'Wholesale', title: 'Wholesale' },
];

export const RESPONSE = {
  HTTP_STATUS_500: 'The requested operation could not be performed. Please try again later or contact support if the issue persists .',
  GET_ALLOCATION_EMPTY: "IMSI allocations haven't been created yet for chosen account.",
  GET_ALLOCATION_SEARCH_EMPTY: 'IMSI allocations not found.',
  SET_SIM_ACTIVATE: 'SIM activate request received successfully!',
  SET_SIM_ACTIVATE_ERROR: 'Failed to activate. Please, try again.',
  SET_SIM_DEACTIVATE: 'SIM deactivate request received successfully!',
  SET_SIM_DEACTIVATE_ERROR: 'Failed to deactivate. Please, try again.',

  GET_AUIDITTRAIL_ERROR: 'Failed to load audit trail. Please, try again.',
  GET_CONNECTIONHISTORY_ERROR: 'Failed to load connection history. Please, try again.',
  GET_MARKETSHARE_EMPTY: 'No Data available for selected period',
  GET_AUIDITTRAIL_EMPTY: 'There is no audit trail available.',
  GET_CONNECTIONHISTORY_EMPTY: 'There is no connection history available.',

  SOMETHING_WENT_WRONG: 'Something went wrong. Please, try again.',

  CREATE_ACCOUNT_SUCCESS: 'Account was created successfully!',
  CREATE_ACCOUNT_ERROR: 'Failed to create account. Please, try again.',
  UPDATE_ACCOUNT_SUCCESS: 'Account was updated successfully!',
  UPDATE_ACCOUNT_ERROR: 'Failed to update account. Please, try again.',
  GET_ACCOUNTS_ERROR: 'No available accounts found. Try reloading the page.',
  GET_ACCOUNT_ERROR: 'Account is not found. Try reloading the page',

  UPDATE_SIM_CARD_CHARGE_SUCCESS: 'SIM Card Charge was updated successfully.',
  CREATE_SIM_CARD_CHARGE_SUCCESS: 'SIM Card Charge was created successfully.',

  CREATE_PAYMENT_TERMS_SUCCESS: 'Payment Terms were created successfully.',
  UPDATE_PAYMENT_TERMS_SUCCESS: 'Payment Terms were updated successfully.',

  DIFFERENT_STATUS_SIM_SELECTION_ERROR: 'Multiple status changes aren’t available for selected SIMs with different statuses',
  GET_SMS_CONNECTIONHISTORY_EMPTY: 'No Data available for selected period',
  GET_VOICE_CONNECTIONHISTORY_EMPTY: 'No Data available for selected period',
  NO_MSISDN: 'No MSISDN numbers available',
  MSISDN_TYPE_CASE: '"Voice/SMS/Data" is unavailable with "International."',
  GET_SIMACTION_EMPTY: 'There is no SIM actions available.',
  LOCATION_HISTORY_EMPTY: "No available location history found.",
};

export const BYTES_TO_MB_DIVIDER = 1048576;

export const CHARTCOLORS = [
  '#00C9E7',
  '#6EC9A7',
  '#AA8ADA',
  '#F984FA',
  '#F18F93',
  '#81CBEA',
  '#98D9C0',
  '#FBB9AE',
  '#80E4F3',
  '#6F37BF',
  '#DC00E0',
  '#EE777B',
  '#28A6DA',
  '#4FBE94',
  '#F88875',
  '#26D1EB',
  '#2A068C',
  '#999999',
  '#D9D9D9',
];
export const REPOSITORY = {
  SIM_MANAGEMENT: 'SIM Details',
  SIM_ORDERS: 'SIM Orders',
  MARKET_SHARE_REPORT: 'Reporting',
  RATE_PLAN: 'Rate Plans',
  ACCOUNT_MANAGEMENT: 'Accounts',
  AUDIT: 'Audit',
};

export const PERMISSION = {
  CREATE_ALLOCATION: 'Create Allocation',
  GET_SIM_REMAINS: 'View SIM Remains',
  UPDATE_SIM_CARD_DETAILS: 'Update MSISDN Records',
  BULK_UPDATE_SIM_CARD_DETAILS: 'Update Multiple MSISDN Records',
  MSISDN_POOL_DETAILS: 'View MSISDN Records',
  EXPORT_MSISDN_LIST: 'Export MSISDN List',
  FREE_MSISDN_COUNT: 'View Available MSISDN Count',
  UPLOAD_MSISDN: 'Upload MSISDN',
  UPDATE_SELECTED_MSISDN_RECORDS: 'Update Selected MSISDN Records',
};

export const ROUTE_PERMISSION = {
  // sim management
  EXPORT_SIM_MANAGEMENT: 'Export SIM Information', // ok
  GET_SIM_STATUS: 'View SIM Status', //
  GET_ALLOCATIONS: 'View Allocations', // ok
  // DELETE_RANGE: 'Delete Range', //
  // DELETE_ALLOCATIONS_BY_RANGE: 'Delete Allocations By Range', //
  // DELETE_ALLOCATION: 'Delete Allocation', //
  SIM_CONNECTION_SUMMARY: 'View SIM Connection Summary', //
  SIM_CONNECTION_SUMMARY_EXPORT: 'SIM Connection Summary Export', //
  GET_RANGES: 'View Ranges',
  GET_SIM_REMAINS: 'View SIM Remains',
  SIM_ACTIVATE: 'SIM Activate', // active sim
  SIM_CEASE: 'SIM Deactivate',
  EXPORT_CONNECTION_HISTORY: 'Export Connection History',
  CREATE_ALLOCATION: 'Create Allocation',
  GET_SMS_CONNECTION_HISTORY: 'View SMS Connection History',
  GET_SMS_CONNECTION_HISTORY_EXPORT: 'Export SMS Connection History',
  SIM_AUDIT_LOGS: 'SIM Audit Logs',
  GET_CONNECTION_HISTORY: 'View Connection History',
  SIM_PROVIDE: 'SIM Activate',
  GET_SIM_MANAGEMENT: 'View SIM Information', // get_sims_usage
  GET_VOICE_CONNECTION_HISTORY: 'View Voice Connection History',
  GET_VOICE_CONNECTION_HISTORY_EXPORT: 'Export Voice Connection History',
  GET_ACCOUNT_NAMES: 'View Account Names',
  FLUSH_SESSION: "Send Cancel Location",
  SEND_SMS: "Send SMS",
  TERMINATE_SESSION: "Disconnect POD",
  // rate-plans
  DELETE_RATE_PLAN: 'Delete Rate Plan', // remove_rate_plan
  VIEW_RATE_PLAN: 'View Rate Plan', // rate_plan_details
  MODIFY_RATE_PLAN: 'Modify Rate Plan', // update_rate_plan
  GET_RATE_PLANS: 'View Rate Plans', // rate_plans_by_accounts
  CREATE_RATE_PLAN: 'Create Rate Plan', // create_rate_plan
  GET_CLIENT_RATE_PLANS: 'View Client Rate Plans', // rate_plans
  // accounts
  VIEW_ACCOUNTS: 'View Accounts',
  // market share
  MARKET_SHARE_FOR_ACCOUNT: 'View Market Share For Account',
  MARKET_SHARE_FOR_SIM: 'View Market Share For SIM',
  // AUDIT LOG
  EXPORT_TRACE_ERROR_DATA: "Export Request Error Data",
  VIEW_TRACE_DATA: "View Request Data",
  ACCOUNT_AUDIT_LOGS: "Account Audit Logs",
  VIEW_ACCOUNT_ACTIVITY_DETAILS: "View Account Activity Details",
  VIEW_TRACE_DETAILS: "View Request Details",
  VIEW_ACCOUNT_ACTIVITY_LOGS: "View Account Activity Logs",
  // SIM ORDERS
  VIEW_ORDERS: 'View Orders',
  VIEW_ORDER_DETAILS: 'View Order Details',
  UPDATE_ORDER_STATUS: 'Update Order Status',
  CREATE_ORDER: 'Create Order',
  // SIM LOCATION
  VIEW_CELL_LOCATION : 'View Cell Location',
  VIEW_LATEST_LOCATION_DATA : 'View SIM Latest Details',
  VIEW_LOCATION_DATA : 'View Location History'
};

export const statusColorMap = (theme) => ({
  active: theme?.palette?.success.light,
  totalactive: theme?.palette?.success.light,
  completed: theme?.palette?.success.light,
  error: '#AA8ADA',
  deactivated: '#F7735D',
  failed: '#F7735D',
  totalothers: '#AA8ADA',
  'ready for activation': '#C2C2C2',
  updated: '#0095e1',
  're-allocated': '#0095e1',
  allocated: '#0095e1',
  'file received': '#0095e1',
  unknown: '#FFE426',
  pending: '#FFE426', 
  'partially completed': '#ff9900',
  'disconnect data session pod': "#5514B4",
  'send cancel location': "#0296D4",
  standard : '#30B281',
  micro : '#00C9E7',
  nano : '#F99D8E',
  esim : '#EB5F64',
  'esim euicc' : '#C6C7D5',
});


export const simOrderStatusColorMap = () => ({
  pending: '#0296D4',
  'pending approval': '#0296D4',
  approved: '#30B281;',
  rejected: '#F7735D',
  cancelled: '#F7735D',
  shipped: '#5514B4',
  approve: '#30B281;',
  cancel: '#F7735D',
  'on hold': '#FFDF00',
  ship: '#5514B4',
  onhold: '#FFDF00',
});

export const capitalizeFirstLetter = (str: string) => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}
