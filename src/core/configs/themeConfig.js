import BTLogo from 'assets/images/bt-logo.svg';
import styles from '@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss';
import nextgenLogo from 'assets/images/ts-nextgen-header-logo.svg';
import TNSLogo from 'assets/images/tns-logo.svg';
import TNSLogoMob from 'assets/images/ts-nextgen-header-logo-mob.svg';

const themeConfig = {
  nextgen: {
    primaryColor: styles.brandBlueColor500,
    secondaryColor: styles.yellowColor500,
    logo: nextgenLogo,
    logoMob: nextgenLogo,
  },
  bt: {
    primaryColor: '#5514B4',
    secondaryColor: '#F200F5',
    logo: BTL<PERSON>,
    logoMob: BTLogo,
    typography: {
      fontFamily: [
        'BTCurve, sans-serif',
      ],
    },
  },
  TNS: {
    primaryColor: '#007AC2',
    secondaryColor: '#95C11F',
    logo: TNSLogo,
    logoMob: TNSLogoMob,
  },
  telstra: {
    primaryColor: '#6F2C91',
    secondaryColor: '#00A9CE',
    logo: nextgenLogo, // Using nextgen logo as fallback until Telstra logo is available
    logoMob: nextgenLogo,
  },
};

export default themeConfig;
