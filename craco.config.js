
const sassResourcesLoader = require('craco-sass-resources-loader');
const ModuleFederationPlugin = require('webpack/lib/container/ModuleFederationPlugin');
const { whenDev, whenProd } = require('@craco/craco');
const { dependencies } = require('./package.json');

const isLocalDevelopment = process.env.NODE_ENV === 'development';
const isStandalone = process.env.REACT_APP_STANDALONE === 'true';

const createSharedConfig = (packageName, options = {}) => {
  const baseConfig = {
    singleton: true,
    requiredVersion: dependencies[packageName],
    ...options,
  };

  if (isLocalDevelopment || isStandalone) {
    baseConfig.eager = true;
  }

  return baseConfig;
};

module.exports = {
  typescript: {
    enableTypeChecking: process.env.NODE_ENV === 'development',
  },
  plugins: [
    {
      plugin: sassResourcesLoader,
      options: {
        resources: [
          'node_modules/@nv2/nv2-pkg-js-theme/src/components/styles/variables.module.scss',
          'src/assets/styles/variables.scss',
        ],
      },
    },
  ],
  webpack: {
    configure: (webpackConfig) => {

      webpackConfig.plugins = [
        ...webpackConfig.plugins,
        new ModuleFederationPlugin({
          name: 'spogSimManagement',
          filename: 'remoteEntry.js?v=1.0.24',
          exposes: {
            './spogSimManagement': './src/App',
          },
          shared: {
            react: createSharedConfig('react', { eager: true }),
            'react-dom': createSharedConfig('react-dom', { eager: true }),

            // All other dependencies - eager in development, not eager in production
            'react-router-dom': createSharedConfig('react-router-dom'),
            '@mui/material': createSharedConfig('@mui/material'),
            '@mui/styles': createSharedConfig('@mui/styles'),
            '@mui/icons-material': createSharedConfig('@mui/icons-material'),
            '@mui/lab': createSharedConfig('@mui/lab'),
            axios: createSharedConfig('axios'),
            'react-cookie': createSharedConfig('react-cookie'),
            'react-toastify': createSharedConfig('react-toastify'),
            formik: createSharedConfig('formik'),
            yup: createSharedConfig('yup'),
            dayjs: createSharedConfig('dayjs'),
            lodash: createSharedConfig('lodash'),
            'react-dropzone': createSharedConfig('react-dropzone'),
            'react-country-flag': createSharedConfig('react-country-flag'),
            'socket.io-client': createSharedConfig('socket.io-client'),
            recharts: createSharedConfig('recharts'),
          },
        }),
      ];


      // webpackConfig.output.publicPath = 'auto';

      webpackConfig.output = {
        ...webpackConfig.output,
        // for start as container part
        // ...whenDev(() => ({ publicPath: 'auto', clean: true })),
        // for start as independent application
        ...whenDev(() => ({ publicPath: '/', clean: true })),
        ...whenProd(() => ({ publicPath: 'auto', clean: true })),
      };

      return webpackConfig;
    },
  },
};
